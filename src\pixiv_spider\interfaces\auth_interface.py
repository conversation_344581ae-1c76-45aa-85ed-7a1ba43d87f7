"""
认证服务接口
"""

from abc import ABC, abstractmethod
from typing import Tuple, List, Dict, Any, Optional


class IAuthService(ABC):
    """认证服务接口"""
    
    @abstractmethod
    def check_login_status(self) -> Tuple[bool, Optional[List[Dict[str, Any]]]]:
        """
        检查登录状态
        
        Returns:
            Tuple[bool, Optional[List[Dict]]]: (是否已登录, cookies)
        """
        pass
    
    @abstractmethod
    def save_cookies(self, cookies: List[Dict[str, Any]]) -> bool:
        """
        保存cookies
        
        Args:
            cookies: cookie列表
            
        Returns:
            bool: 是否保存成功
        """
        pass
    
    @abstractmethod
    def clear_cookies(self) -> bool:
        """
        清除保存的cookies
        
        Returns:
            bool: 是否清除成功
        """
        pass
    
    @abstractmethod
    def validate_cookies(self, cookies: List[Dict[str, Any]]) -> bool:
        """
        验证cookies有效性
        
        Args:
            cookies: cookie列表
            
        Returns:
            bool: cookies是否有效
        """
        pass
