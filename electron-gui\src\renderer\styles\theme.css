/* Pixiv Spider v6.0 主题样式 */

:root {
  /* 浅色主题 */
  --bg-primary: #ffffff;
  --bg-secondary: #f5f7fa;
  --bg-tertiary: #ebeef5;
  --text-primary: #303133;
  --text-secondary: #606266;
  --text-tertiary: #909399;
  --text-muted: #c0c4cc;
  --border-color: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --shadow-light: rgba(0, 0, 0, 0.12);
  --shadow-base: rgba(0, 0, 0, 0.16);
  
  /* 品牌色 */
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --error-color: #f56c6c;
  --info-color: #909399;

  /* 阴影 */
  --shadow: var(--shadow-base);
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --text-primary: #e5eaf3;
  --text-secondary: #cfd3dc;
  --text-tertiary: #a3a6ad;
  --text-muted: #8b949e;
  --border-color: #4c4d4f;
  --border-light: #414243;
  --border-lighter: #363637;
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-base: rgba(0, 0, 0, 0.4);
  --shadow: var(--shadow-base);
}

/* 深色主题类名兼容 */
.dark-theme {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --text-primary: #e5eaf3;
  --text-secondary: #cfd3dc;
  --text-tertiary: #a3a6ad;
  --text-muted: #8b949e;
  --border-color: #4c4d4f;
  --border-light: #414243;
  --border-lighter: #363637;
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-base: rgba(0, 0, 0, 0.4);
  --shadow: var(--shadow-base);
}

/* 全局样式 */
* {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'Segoe UI', Arial, sans-serif;
}

body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'Segoe UI', Arial, sans-serif;
}

/* Element Plus 组件主题覆盖 */
.el-card,
.el-card__header,
.el-card__body,
.el-button:not(.el-button--primary):not(.el-button--warning),
.el-button--default,
.el-button--text,
.el-input__wrapper,
.el-input-number__wrapper,
.el-input-number,
.el-input__inner,
.el-input-number__inner,
.el-input-number__increase,
.el-input-number__decrease,
.el-textarea__inner,
.el-select .el-input__wrapper,
.el-select__wrapper,
.el-select-dropdown,
.el-select-dropdown__item,
.el-table,
.el-table__body,
.el-table__header,
.el-table__header-wrapper,
.el-table th,
.el-table td,
.el-table__row,
.el-table--border,
.el-dialog,
.el-dialog__header,
.el-dialog__body,
.el-alert,
.el-message,
.el-notification,
.el-drawer,
.el-popover,
.el-tooltip__popper,
.el-dropdown-menu,
.el-dropdown-menu__item,
.el-menu,
.el-menu-item,
.el-submenu__title,
.el-checkbox__inner,
.el-radio__inner,
.el-form-item__label,
.el-pagination,
.el-pager li,
.el-descriptions,
.el-descriptions__header,
.el-descriptions__title,
.el-descriptions__body,
.el-descriptions-item,
.el-descriptions-item__label,
.el-descriptions-item__content,
h1, h2, h3, h4, h5, h6,
.title,
.subtitle,
.header-title,
.card-title {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.el-switch__core,
.el-progress-bar__outer,
.el-slider__runway {
  background-color: var(--bg-tertiary) !important;
}

/* 强制表格和描述列表深色模式 */
.el-descriptions .el-descriptions-item__label,
.el-descriptions .el-descriptions-item__content,
.el-descriptions__table,
.el-descriptions__table td,
.el-descriptions__table th,
.el-descriptions__table tr,
.el-descriptions-item__cell {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* 分割线和小标题 */
.el-divider,
.el-divider__text {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

/* 深色主题特定样式 */
.dark-theme .el-card,
[data-theme="dark"] .el-card {
  box-shadow: 0 2px 12px 0 var(--shadow-base) !important;
}

.dark-theme .el-button--primary,
[data-theme="dark"] .el-button--primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.dark-theme .el-input__wrapper:hover,
[data-theme="dark"] .el-input__wrapper:hover {
  border-color: var(--primary-color) !important;
}

.dark-theme .el-input__wrapper.is-focus,
[data-theme="dark"] .el-input__wrapper.is-focus {
  border-color: var(--primary-color) !important;
}

/* 自定义组件样式 */
.login-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.status-display {
  text-align: center;
  margin: 15px 0;
}

.login-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.login-actions .el-button {
  flex: 1;
}

/* 登录对话框样式 */
.login-dialog {
  padding: 20px 0;
}

.status-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.status-icon {
  font-size: 48px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: var(--text-primary);
}

.status-text p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.progress-bar {
  margin: 20px 0;
}

.login-steps {
  margin: 20px 0;
}

.steps-list {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.steps-list li {
  margin: 8px 0;
  line-height: 1.5;
}

.error-alert {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-actions {
    flex-direction: column;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
  
  .status-section {
    flex-direction: column;
    text-align: center;
  }
  
  .status-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 过渡动画 */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}
