"""
Pixiv Spider 安装配置
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding='utf-8') if readme_file.exists() else ""

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
else:
    requirements = [
        "requests>=2.25.0",
        "selenium>=4.0.0",
        "beautifulsoup4>=4.9.0",
        "pillow>=8.0.0",
        "lxml>=4.6.0",
    ]

setup(
    name="pixiv-spider",
    version="6.0.0",
    description="现代化桌面Pixiv作品爬虫工具 - 基于Electron + Vue + Python IPC架构",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Pixiv Spider Team",
    author_email="",
    url="https://github.com/your-username/pixiv-spider",
    license="MIT",
    
    # 包配置
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    
    # Python版本要求
    python_requires=">=3.8",
    
    # 依赖
    install_requires=requirements,
    
    # 可选依赖
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-cov>=2.10.0",
            "black>=21.0.0",
            "flake8>=3.8.0",
            "mypy>=0.812",
        ],
        "gui": [
            "tkinter",  # 传统GUI支持
        ],
        "electron": [
            # Electron GUI需要Node.js环境，不是Python包
        ]
    },
    
    # 分类
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Multimedia :: Graphics",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    
    # 关键词
    keywords="pixiv spider crawler download artwork illustration",
    
    # 入口点
    entry_points={
        "console_scripts": [
            "pixiv-spider=pixiv_spider.main:main",
            "pixiv-spider-gui=start:main",
            "pixiv-spider-start=start:main",
        ],
    },
    
    # 包含额外文件
    include_package_data=True,
    package_data={
        "pixiv_spider": [
            "config/*.json",
            "gui/icons/*.png",
            "gui/styles/*.css",
        ],
    },
    
    # 项目URLs
    project_urls={
        "Bug Reports": "https://github.com/your-username/pixiv-spider/issues",
        "Source": "https://github.com/your-username/pixiv-spider",
        "Documentation": "https://github.com/your-username/pixiv-spider/wiki",
    },
) 