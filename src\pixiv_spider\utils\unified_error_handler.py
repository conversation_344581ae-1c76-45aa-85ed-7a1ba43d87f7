"""
统一错误处理器
解决前后端重复的错误处理逻辑
"""

import logging
from typing import Dict, Any, Optional, Union
from enum import Enum
import traceback

from ..models.exceptions import (
    PixivSpiderError,
    AuthenticationError,
    NetworkError,
    ConfigError as ConfigurationError
)


class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "NETWORK_ERROR"
    CONNECTION_ERROR = "CONNECTION_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    AUTH_ERROR = "AUTH_ERROR"
    LOGIN_REQUIRED = "LOGIN_REQUIRED"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    CONFIG_ERROR = "CONFIG_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    DOWNLOAD_ERROR = "DOWNLOAD_ERROR"
    FILE_ERROR = "FILE_ERROR"
    SYSTEM_ERROR = "SYSTEM_ERROR"
    UNKNOWN_ERROR = "UNKNOWN_ERROR"


class UnifiedErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 错误消息映射
        self.error_messages = {
            ErrorType.NETWORK_ERROR: '网络连接失败，请检查网络设置',
            ErrorType.CONNECTION_ERROR: '无法连接到服务器，请稍后重试',
            ErrorType.TIMEOUT_ERROR: '请求超时，请检查网络连接',
            ErrorType.AUTH_ERROR: '认证失败，请重新登录',
            ErrorType.LOGIN_REQUIRED: '请先登录Pixiv账户',
            ErrorType.TOKEN_EXPIRED: '登录已过期，请重新登录',
            ErrorType.CONFIG_ERROR: '配置错误，请检查设置',
            ErrorType.VALIDATION_ERROR: '输入验证失败，请检查输入内容',
            ErrorType.DOWNLOAD_ERROR: '下载失败，请重试',
            ErrorType.FILE_ERROR: '文件操作失败，请检查文件权限',
            ErrorType.SYSTEM_ERROR: '系统错误，请联系技术支持',
            ErrorType.UNKNOWN_ERROR: '未知错误，请重试'
        }
    
    def handle_api_error(self, error: Exception, context: str = '') -> Dict[str, Any]:
        """处理API错误"""
        error_info = self._parse_error(error)
        
        # 记录错误日志
        self._log_error(error_info, context)
        
        # 返回标准化的错误信息
        return {
            'success': False,
            'error_type': error_info['type'].value,
            'message': error_info['user_message'],
            'context': context,
            'details': error_info.get('details', {})
        }
    
    def handle_network_error(self, error: Exception) -> Dict[str, Any]:
        """处理网络错误"""
        error_type = ErrorType.NETWORK_ERROR
        
        error_msg = str(error).lower()
        if 'connection' in error_msg or 'refused' in error_msg:
            error_type = ErrorType.CONNECTION_ERROR
        elif 'timeout' in error_msg:
            error_type = ErrorType.TIMEOUT_ERROR
        
        return {
            'success': False,
            'error_type': error_type.value,
            'message': self.error_messages[error_type],
            'original_error': str(error)
        }
    
    def handle_auth_error(self, error: Exception) -> Dict[str, Any]:
        """处理认证错误"""
        error_type = ErrorType.AUTH_ERROR
        
        error_msg = str(error).lower()
        if 'login' in error_msg or '登录' in error_msg:
            error_type = ErrorType.LOGIN_REQUIRED
        elif 'token' in error_msg or 'expired' in error_msg:
            error_type = ErrorType.TOKEN_EXPIRED
        
        return {
            'success': False,
            'error_type': error_type.value,
            'message': self.error_messages[error_type],
            'original_error': str(error)
        }
    
    def handle_config_error(self, error: Exception, validation_errors: list = None) -> Dict[str, Any]:
        """处理配置错误"""
        return {
            'success': False,
            'error_type': ErrorType.CONFIG_ERROR.value,
            'message': self._format_validation_errors(validation_errors) if validation_errors else self.error_messages[ErrorType.CONFIG_ERROR],
            'validation_errors': validation_errors or [],
            'original_error': str(error)
        }
    
    def handle_download_error(self, error: Exception) -> Dict[str, Any]:
        """处理下载错误"""
        error_type = ErrorType.DOWNLOAD_ERROR
        
        error_msg = str(error).lower()
        if 'file' in error_msg or 'permission' in error_msg or '权限' in error_msg:
            error_type = ErrorType.FILE_ERROR
        
        return {
            'success': False,
            'error_type': error_type.value,
            'message': self.error_messages[error_type],
            'original_error': str(error)
        }
    
    def _parse_error(self, error: Exception) -> Dict[str, Any]:
        """解析错误信息"""
        # 根据异常类型确定错误类型
        if isinstance(error, AuthenticationError):
            error_type = ErrorType.AUTH_ERROR
        elif isinstance(error, NetworkError):
            error_type = ErrorType.NETWORK_ERROR
        elif isinstance(error, ConfigurationError):
            error_type = ErrorType.CONFIG_ERROR
        elif isinstance(error, PixivSpiderError):
            error_type = self._detect_error_type(str(error))
        else:
            error_type = self._detect_error_type(str(error))
        
        return {
            'type': error_type,
            'user_message': self.error_messages[error_type],
            'original_message': str(error),
            'details': {
                'exception_type': type(error).__name__,
                'traceback': traceback.format_exc() if self.logger.isEnabledFor(logging.DEBUG) else None
            }
        }
    
    def _detect_error_type(self, message: str) -> ErrorType:
        """检测错误类型"""
        msg = message.lower()
        
        if any(keyword in msg for keyword in ['auth', 'login', '认证', '登录']):
            return ErrorType.AUTH_ERROR
        
        if any(keyword in msg for keyword in ['network', 'connection', '网络', '连接']):
            return ErrorType.NETWORK_ERROR
        
        if any(keyword in msg for keyword in ['timeout', '超时']):
            return ErrorType.TIMEOUT_ERROR
        
        if any(keyword in msg for keyword in ['config', 'validation', '配置', '验证']):
            return ErrorType.CONFIG_ERROR
        
        if any(keyword in msg for keyword in ['download', 'file', '下载', '文件']):
            return ErrorType.DOWNLOAD_ERROR
        
        return ErrorType.UNKNOWN_ERROR
    
    def _log_error(self, error_info: Dict[str, Any], context: str):
        """记录错误日志"""
        log_message = f"[{error_info['type'].value}] {context}: {error_info['original_message']}"
        
        # 根据错误类型选择日志级别
        if error_info['type'] in [ErrorType.SYSTEM_ERROR, ErrorType.UNKNOWN_ERROR]:
            self.logger.error(log_message)
            if error_info.get('details', {}).get('traceback'):
                self.logger.debug(error_info['details']['traceback'])
        elif error_info['type'] in [ErrorType.AUTH_ERROR, ErrorType.CONFIG_ERROR]:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def _format_validation_errors(self, errors: list) -> str:
        """格式化验证错误"""
        if not errors:
            return self.error_messages[ErrorType.VALIDATION_ERROR]
        
        if len(errors) == 1:
            return errors[0]
        
        return '; '.join(errors)
    
    def create_success_response(self, data: Any = None, message: str = '操作成功') -> Dict[str, Any]:
        """创建成功响应"""
        response = {
            'success': True,
            'message': message
        }
        
        if data is not None:
            response['data'] = data
        
        return response
    
    def is_error_type(self, error: Exception, error_type: ErrorType) -> bool:
        """检查是否为特定类型的错误"""
        error_info = self._parse_error(error)
        return error_info['type'] == error_type
    
    def get_retry_advice(self, error_type: ErrorType) -> list:
        """获取错误的重试建议"""
        advice_map = {
            ErrorType.NETWORK_ERROR: ['检查网络连接', '稍后重试', '检查防火墙设置'],
            ErrorType.CONNECTION_ERROR: ['检查服务器状态', '稍后重试', '检查网络配置'],
            ErrorType.TIMEOUT_ERROR: ['检查网络速度', '稍后重试', '减少并发请求数'],
            ErrorType.AUTH_ERROR: ['重新登录', '检查账户状态', '清除浏览器缓存'],
            ErrorType.CONFIG_ERROR: ['检查配置设置', '重置为默认配置', '联系技术支持'],
            ErrorType.DOWNLOAD_ERROR: ['检查磁盘空间', '检查文件权限', '更换下载路径'],
        }
        
        return advice_map.get(error_type, ['重试操作', '重启应用', '联系技术支持'])


# 创建全局实例
unified_error_handler = UnifiedErrorHandler()
