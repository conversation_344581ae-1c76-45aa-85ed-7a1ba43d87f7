#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pixiv Spider IPC API Server

基于IPC通信的API服务器，替换原有的HTTP/WebSocket服务器
通过stdin/stdout与Electron主进程进行JSON消息通信
"""

import sys
import json
import logging
import threading
import time
from typing import Dict, Any, Optional
from pathlib import Path

try:
    from .container.service_container import ServiceContainer
    from .container.service_registry import ServiceRegistry
    from .controllers.spider_controller import SpiderController
    from .config.config_manager import ConfigManager
    from .models.exceptions import PixivSpiderError
    from .models.config import DownloadConfig
    from .utils.unified_validator import unified_validator
    from .utils.unified_error_handler import unified_error_handler
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from pixiv_spider.container.service_container import ServiceContainer
    from pixiv_spider.container.service_registry import ServiceRegistry
    from pixiv_spider.controllers.spider_controller import Spider<PERSON>ontroller
    from pixiv_spider.config.config_manager import ConfigManager
    from pixiv_spider.models.exceptions import PixivSpiderError
    from pixiv_spider.models.config import DownloadConfig
    from pixiv_spider.utils.unified_validator import unified_validator
    from pixiv_spider.utils.unified_error_handler import unified_error_handler


class FrontendLogHandler(logging.Handler):
    """将日志消息转发到前端的处理器"""

    def __init__(self, api_server):
        super().__init__()
        self.api_server = api_server

    def emit(self, record):
        try:
            # 格式化日志消息
            message = self.format(record)

            # 发送到前端
            if self.api_server:
                self.api_server._send_log_message(
                    level=record.levelname,
                    message=message,
                    logger_name=record.name
                )
        except Exception:
            # 避免日志处理器本身出错
            pass


class PixivSpiderAPI:
    """
    Pixiv Spider IPC API服务器
    
    通过stdin/stdout进行IPC通信，处理来自Electron主进程的请求
    """
    
    def __init__(self):
        """初始化API服务器"""
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.request_handlers = {}

        # 延迟初始化的组件
        self.container = None
        self.registry = None
        self.config_manager = None
        self.spider_controller = None

        # 登录状态管理
        self.login_in_progress = False
        self.selenium_driver = None
        self.auth_service = None

        # 注册请求处理器
        self._register_handlers()

        # 设置日志输出到stderr，避免与stdout通信冲突
        self._setup_logging()

        # 设置日志转发到前端
        self._setup_frontend_logging()
    
    def _setup_logging(self):
        """设置日志输出"""
        # 移除所有现有的处理器
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        
        # 创建stderr处理器
        handler = logging.StreamHandler(sys.stderr)
        handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        
        # 设置根日志器
        logging.root.addHandler(handler)
        logging.root.setLevel(logging.ERROR)

    def _setup_frontend_logging(self):
        """设置前端日志转发"""
        # 创建前端日志处理器
        frontend_handler = FrontendLogHandler(self)
        frontend_handler.setLevel(logging.INFO)
        frontend_handler.setFormatter(logging.Formatter('%(message)s'))

        # 添加到特定的日志器
        loggers_to_forward = [
            'pixiv_spider.utils.selenium_utils',
            'pixiv_spider.core.pixiv_spider',
            'pixiv_spider.controllers.spider_controller'
        ]

        for logger_name in loggers_to_forward:
            logger = logging.getLogger(logger_name)
            logger.addHandler(frontend_handler)
            logger.setLevel(logging.INFO)  # 只输出错误，进一步减少日志
    
    def _register_handlers(self):
        """注册请求处理器"""
        self.request_handlers = {
            # 认证相关
            'auth/status': self._handle_auth_status,
            'auth/login': self._handle_auth_login,
            'auth/check_cookies': self._handle_auth_check_cookies,
            'auth/start_selenium': self._handle_auth_start_selenium,
            'auth/confirm_login': self._handle_auth_confirm_login,
            'auth/cancel_login': self._handle_auth_cancel_login,

            # 下载相关
            'download/start': self._handle_download_start,
            'download/stop': self._handle_download_stop,
            'download/pause': self._handle_download_pause,
            'download/resume': self._handle_download_resume,
            'download/status': self._handle_download_status,

            # 配置相关
            'config/get': self._handle_config_get,
            'config/update': self._handle_config_update,

            # 健康检查
            'health': self._handle_health_check,
        }
    
    def run(self):
        """运行IPC服务器"""
        self.running = True
        self.logger.info("Pixiv Spider IPC API服务器启动")

        try:
            # 快速发送启动完成事件，延迟初始化重组件
            self._send_event('server/ready', {'status': 'ready'})

            # 主消息循环
            self._message_loop()

        except Exception as e:
            self.logger.error(f"IPC服务器运行错误: {e}")
            self._send_event('server/error', {'error': str(e)})
        finally:
            self.running = False
            self._cleanup_resources()
            self.logger.info("IPC服务器已停止")

    def _cleanup_resources(self):
        """清理所有资源"""
        try:
            self.logger.info("🧹 开始清理API服务器资源...")

            # 设置停止标志
            self.running = False

            # 清理spider_controller
            if hasattr(self, 'spider_controller') and self.spider_controller:
                try:
                    self.logger.info("🕷️ 正在清理爬虫控制器...")
                    # 如果spider_controller有清理方法，调用它
                    if hasattr(self.spider_controller, 'cleanup'):
                        self.spider_controller.cleanup()
                    elif hasattr(self.spider_controller, 'spider') and self.spider_controller.spider:
                        # 清理spider中的所有资源
                        spider = self.spider_controller.spider

                        # 停止下载
                        if hasattr(spider, 'stop_download'):
                            spider.stop_download()

                        # 清理selenium驱动器
                        if hasattr(spider, 'selenium_driver') and spider.selenium_driver:
                            spider.selenium_driver.quit()
                            spider.selenium_driver = None

                        # 清理下载服务
                        if hasattr(spider, 'download_service') and spider.download_service:
                            spider.download_service.cleanup_resources()

                        # 清理API服务
                        if hasattr(spider, 'api_service'):
                            spider.api_service = None

                    self.spider_controller = None
                    self.logger.info("✅ 爬虫控制器已清理")

                except Exception as e:
                    self.logger.warning(f"❌ 清理spider_controller失败: {e}")

            # 清理Selenium驱动器
            self._cleanup_selenium()

            # 清理其他资源
            try:
                # 清理配置管理器
                if hasattr(self, 'config_manager'):
                    self.config_manager = None

                # 清理回调函数
                if hasattr(self, '_message_handlers'):
                    self._message_handlers.clear()

                self.logger.info("🗑️ 其他资源已清理")

            except Exception as e:
                self.logger.error(f"❌ 清理其他资源失败: {e}")

            self.logger.info("✅ API服务器资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 资源清理失败: {e}")
            import traceback
            self.logger.debug(f"清理异常堆栈: {traceback.format_exc()}")
    
    def _ensure_initialized(self):
        """确保组件已初始化（延迟初始化）- 修复服务实例一致性"""
        if self.container is None:
            self.container = ServiceContainer()
            self.registry = ServiceRegistry(self.container)
            self.config_manager = ConfigManager()

            # 确保容器使用同一个ConfigManager实例
            self.container.register_config_manager(self.config_manager)

        if self.spider_controller is None:
            # 使用共享的容器，确保所有服务使用相同的ConfigManager
            self.spider_controller = SpiderController(container=self.container)

            # 设置状态回调
            if hasattr(self.spider_controller, 'set_status_callback'):
                self.spider_controller.set_status_callback(self._on_status_update)

        if self.auth_service is None:
            from .services.auth_service import AuthService
            # 使用容器中的ConfigManager，确保一致性
            self.auth_service = AuthService(self.container.get_config_manager())
    
    def _message_loop(self):
        """主消息循环"""
        while self.running:
            try:
                # 从stdin读取消息
                line = sys.stdin.readline()
                if not line:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                # 解析JSON消息
                try:
                    message = json.loads(line)
                    self._handle_message(message)
                except json.JSONDecodeError as e:
                    self.logger.error(f"JSON解析错误: {e}")
                    self._send_error_response(None, f"JSON解析错误: {e}")
                
            except EOFError:
                # stdin关闭，退出循环
                break
            except Exception as e:
                self.logger.error(f"消息处理错误: {e}")
    
    def _handle_message(self, message: Dict[str, Any]):
        """处理收到的消息"""
        try:
            msg_id = message.get('id')
            msg_type = message.get('type', 'request')
            action = message.get('action')
            data = message.get('data', {})
            
            if not action:
                self._send_error_response(msg_id, "缺少action字段")
                return
            
            # 查找处理器
            handler = self.request_handlers.get(action)
            if not handler:
                self._send_error_response(msg_id, f"未知的action: {action}")
                return
            
            # 执行处理器
            try:
                result = handler(data)
                self._send_response(msg_id, action, result)
            except Exception as e:
                self.logger.error(f"处理器执行错误 {action}: {e}")
                self._send_error_response(msg_id, str(e))
                
        except Exception as e:
            self.logger.error(f"消息处理错误: {e}")
            self._send_error_response(message.get('id'), str(e))
    
    def _send_response(self, msg_id: Optional[str], action: str, data: Any):
        """发送响应消息"""
        response = {
            'id': msg_id,
            'type': 'response',
            'action': action,
            'success': True,
            'data': data
        }
        self._send_message(response)
    
    def _send_error_response(self, msg_id: Optional[str], error: str):
        """发送错误响应"""
        response = {
            'id': msg_id,
            'type': 'response',
            'success': False,
            'error': error
        }
        self._send_message(response)
    
    def _send_event(self, event_type: str, data: Any):
        """发送事件消息"""
        event = {
            'type': 'event',
            'event': event_type,
            'data': data
        }
        self._send_message(event)
    
    def _send_message(self, message: Dict[str, Any]):
        """发送消息到stdout"""
        try:
            # 验证消息是否可序列化
            json_str = json.dumps(message, ensure_ascii=False)
            print(json_str, flush=True)
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            self.logger.error(f"问题消息: {message}")
            # 尝试发送一个简化的错误消息
            try:
                error_msg = {
                    'id': message.get('id'),
                    'type': 'response',
                    'success': False,
                    'error': f'消息序列化失败: {str(e)}'
                }
                simple_json = json.dumps(error_msg, ensure_ascii=False)
                print(simple_json, flush=True)
            except:
                pass  # 如果连错误消息都无法序列化，就放弃
    
    def _on_status_update(self, status_data: Dict[str, Any]):
        """状态更新回调"""
        self._send_event('status_update', status_data)

    def _send_log_message(self, level: str, message: str, logger_name: str = ""):
        """发送日志消息到前端"""
        log_data = {
            'level': level,
            'message': message,
            'logger': logger_name,
            'timestamp': time.time()
        }
        self._send_event('log_message', log_data)

    # 移除废弃的配置转换方法 - 前端现在使用统一配置服务

    # ===== 请求处理器 =====

    def _handle_health_check(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """健康检查"""
        return {
            'status': 'ok',
            'timestamp': time.time(),
            'version': '6.0'
        }

    def _handle_auth_status(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取认证状态"""
        try:
            self._ensure_initialized()

            # 检查认证状态
            if self.auth_service:
                is_logged_in, cookies = self.auth_service.check_login_status()
                return {
                    'authenticated': is_logged_in,
                    'cookies_count': len(cookies) if cookies else 0,
                    'message': '已登录' if is_logged_in else '未登录'
                }
            else:
                return {'authenticated': False, 'error': '认证服务不可用'}

        except Exception as e:
            return unified_error_handler.handle_auth_error(e)

    def _handle_auth_login(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理登录请求 - 智能登录流程"""
        try:
            self._ensure_initialized()
            force_relogin = data.get('force_relogin', False)

            if self.login_in_progress:
                return {
                    'success': False,
                    'message': '登录正在进行中，请稍候',
                    'status': 'in_progress'
                }

            self.login_in_progress = True

            try:
                # 发送登录开始事件
                self._send_event('auth/login_started', {'force_relogin': force_relogin})

                # 1. 首先检查现有cookies
                if not force_relogin:
                    is_logged_in, cookies = self.auth_service.check_login_status()
                    if is_logged_in:
                        self.login_in_progress = False
                        self._send_event('auth/login_success', {'method': 'cookies'})
                        return {
                            'success': True,
                            'message': '使用现有cookies登录成功',
                            'method': 'cookies'
                        }

                # 2. 需要手动登录，启动Selenium
                self._send_event('auth/selenium_starting', {})
                selenium_result = self._start_selenium_login()

                if selenium_result['success']:
                    return {
                        'success': True,
                        'message': '请在浏览器中完成登录',
                        'method': 'selenium',
                        'status': 'waiting_user'
                    }
                else:
                    self.login_in_progress = False
                    return selenium_result

            except Exception as e:
                self.login_in_progress = False
                raise e

        except Exception as e:
            self.logger.error(f"登录失败: {e}")
            self._send_event('auth/login_error', {'error': str(e)})
            raise PixivSpiderError(f"登录失败: {e}")

    def _handle_auth_check_cookies(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """检查cookies状态"""
        try:
            self._ensure_initialized()
            is_logged_in, cookies = self.auth_service.check_login_status()

            return {
                'success': True,
                'is_logged_in': is_logged_in,
                'cookies_count': len(cookies) if cookies else 0,
                'message': '已登录' if is_logged_in else '未登录'
            }

        except Exception as e:
            return unified_error_handler.handle_auth_error(e)

    def _handle_auth_start_selenium(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """启动Selenium登录"""
        try:
            self._ensure_initialized()

            if self.selenium_driver is not None:
                return {
                    'success': False,
                    'message': 'Selenium已在运行中'
                }

            result = self._start_selenium_login()
            return result

        except Exception as e:
            self.logger.error(f"启动Selenium失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _handle_auth_confirm_login(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """确认登录完成"""
        try:
            self._ensure_initialized()

            if self.selenium_driver is None:
                return {
                    'success': False,
                    'message': 'Selenium未启动'
                }

            # 检查登录状态并保存cookies
            result = self._check_and_save_login()

            # 清理Selenium
            self._cleanup_selenium()
            self.login_in_progress = False

            if result['success']:
                self._send_event('auth/login_success', {'method': 'selenium'})
            else:
                self._send_event('auth/login_failed', {'reason': result.get('message', '未知错误')})

            return result

        except Exception as e:
            self.logger.error(f"确认登录失败: {e}")
            self._cleanup_selenium()
            self.login_in_progress = False
            return {
                'success': False,
                'error': str(e)
            }

    def _handle_auth_cancel_login(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """取消登录"""
        try:
            self._cleanup_selenium()
            self.login_in_progress = False
            self._send_event('auth/login_cancelled', {})

            return {
                'success': True,
                'message': '登录已取消'
            }

        except Exception as e:
            self.logger.error(f"取消登录失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _handle_download_start(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """开始下载"""
        try:
            # 设置下载配置
            download_config_data = data.get('config', {})
            self.logger.info(f"📥 收到下载配置")

            # 前端已经转换为后端格式，直接使用
            converted_config = download_config_data
            self.logger.info(f"✅ 使用前端转换的配置")

            # 验证转换后的路径
            final_save_path = converted_config.get('save_path', '')
            self.logger.info(f"📁 最终保存路径: {final_save_path}")

            if not final_save_path:
                self.logger.error("❌ 保存路径为空，使用默认路径")
                converted_config['save_path'] = 'H:\\Pixiv\\Downloads'

            self.logger.info(f"最终配置: {converted_config}")

            # 创建DownloadConfig对象
            download_config = DownloadConfig.from_dict(converted_config)

            self.logger.info(f"创建的下载配置: {download_config.to_dict()}")
            self.logger.info(f"下载模式: {download_config.download_mode}")
            self.logger.info(f"日期模式: {download_config.date_mode}")
            self.logger.info(f"页码范围: {download_config.start_page} - {download_config.end_page}")

            # 使用统一验证器验证配置
            validation_errors = unified_validator.validate_download_config(download_config)
            if validation_errors:
                return unified_error_handler.handle_config_error(
                    Exception("配置验证失败"),
                    validation_errors
                )

            # 异步启动下载任务，避免阻塞API响应
            import threading

            def start_download_async():
                try:
                    self.logger.info("开始异步下载任务，正在初始化...")

                    # 在后台线程中进行初始化
                    self.logger.info("正在调用_ensure_initialized...")
                    self._ensure_initialized()
                    self.logger.info("_ensure_initialized完成")

                    self.logger.info("正在调用spider_controller.start_download_task...")
                    result = self.spider_controller.start_download_task(download_config)
                    self.logger.info(f"start_download_task完成，结果: {result}")

                    # 发送下载完成事件
                    self._send_event('download/completed', {
                        'success': True,
                        'stats': result if result else {}
                    })
                    self.logger.info("下载完成事件已发送")

                    # 下载完成后自动清理Selenium资源
                    self.logger.info("🧹 下载完成，开始清理Selenium资源...")
                    try:
                        if (hasattr(self, 'spider_controller') and
                            self.spider_controller and
                            hasattr(self.spider_controller, 'spider') and
                            self.spider_controller.spider):

                            spider = self.spider_controller.spider
                            if hasattr(spider, '_cleanup_selenium_after_download'):
                                spider._cleanup_selenium_after_download()
                            elif hasattr(spider, 'selenium_driver') and spider.selenium_driver:
                                # 如果没有专门的清理方法，直接清理Selenium驱动器
                                spider.selenium_driver.quit()
                                spider.selenium_driver = None
                                self.logger.info("✅ Selenium驱动器已清理")

                    except Exception as e:
                        self.logger.error(f"❌ 清理Selenium资源失败: {e}")

                except Exception as e:
                    self.logger.error(f"异步下载任务失败: {e}")
                    import traceback
                    self.logger.error(f"异步下载错误详情: {traceback.format_exc()}")
                    # 发送下载失败事件
                    self._send_event('download/failed', {
                        'success': False,
                        'error': str(e)
                    })
                    self.logger.info("下载失败事件已发送")

            # 在后台线程中启动下载
            download_thread = threading.Thread(target=start_download_async, daemon=True)
            download_thread.start()

            # 立即返回启动确认
            response_data = {
                'success': True,
                'message': '下载任务已在后台启动',
                'stats': {'status': 'starting'}
            }

            self.logger.info(f"下载任务已提交到后台线程")
            return response_data

        except Exception as e:
            self.logger.error(f"启动下载失败: {e}")
            import traceback
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return {
                'success': False,
                'message': f"启动下载失败: {e}"
            }

    def _handle_download_stop(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """立即停止下载"""
        try:
            self.logger.info("🛑 API收到停止下载请求，立即处理...")
            self._ensure_initialized()

            # 立即停止下载任务
            self.spider_controller.stop_download_task()

            # 立即返回响应，不等待清理完成
            self.logger.info("✅ 停止信号已发送，下载正在停止...")

            return {
                'success': True,
                'message': '下载停止信号已发送'
            }

        except Exception as e:
            self.logger.error(f"停止下载失败: {e}")
            raise PixivSpiderError(f"停止下载失败: {e}")

    def _handle_download_pause(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """暂停下载"""
        try:
            self._ensure_initialized()
            # 注意：当前SpiderController没有pause方法，使用stop代替
            self.spider_controller.stop_download_task()

            return {
                'success': True,
                'message': '下载已暂停'
            }

        except Exception as e:
            self.logger.error(f"暂停下载失败: {e}")
            raise PixivSpiderError(f"暂停下载失败: {e}")

    def _handle_download_resume(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """继续下载"""
        try:
            # 注意：当前SpiderController没有resume方法
            # 这里返回成功但实际上需要重新启动下载
            return {
                'success': True,
                'message': '下载已继续（需要重新启动下载）'
            }

        except Exception as e:
            self.logger.error(f"继续下载失败: {e}")
            raise PixivSpiderError(f"继续下载失败: {e}")

    def _handle_download_status(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取下载状态"""
        try:
            if self.spider_controller is None:
                return {
                    'status': 'idle',
                    'progress': 0,
                    'message': '控制器未初始化'
                }

            status = self.spider_controller.get_download_status()
            return status

        except Exception as e:
            self.logger.error(f"获取下载状态失败: {e}")
            return {
                'status': 'error',
                'progress': 0,
                'message': str(e)
            }

    def _handle_config_get(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """获取配置"""
        try:
            self._ensure_initialized()
            config = self.config_manager.load_spider_config()
            return {
                'config': config.to_dict() if hasattr(config, 'to_dict') else config.__dict__
            }

        except Exception as e:
            self.logger.error(f"获取配置失败: {e}")
            raise PixivSpiderError(f"获取配置失败: {e}")

    def _handle_config_update(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新配置"""
        try:
            self._ensure_initialized()
            config_data = data.get('config', {})

            # 更新配置
            self.config_manager.update_spider_config(config_data)

            return {
                'success': True,
                'message': '配置已更新'
            }

        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
            raise PixivSpiderError(f"更新配置失败: {e}")

    # ===== Selenium登录辅助方法 =====

    def _start_selenium_login(self) -> Dict[str, Any]:
        """启动Selenium登录流程"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options

            # 设置Chrome选项
            options = Options()
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-gpu')
            options.add_argument('--window-size=1200,800')

            # 启动浏览器
            self.selenium_driver = webdriver.Chrome(options=options)
            self.selenium_driver.get('https://www.pixiv.net/')

            # 等待页面加载
            import time
            time.sleep(3)

            self._send_event('auth/selenium_ready', {
                'message': '浏览器已打开，请在浏览器中登录Pixiv账号'
            })

            return {
                'success': True,
                'message': '浏览器已打开，请在浏览器中登录'
            }

        except Exception as e:
            self.logger.error(f"启动Selenium失败: {e}")
            self._cleanup_selenium()
            return {
                'success': False,
                'error': str(e)
            }

    def _check_and_save_login(self) -> Dict[str, Any]:
        """检查登录状态并保存cookies"""
        try:
            if self.selenium_driver is None:
                return {
                    'success': False,
                    'message': 'Selenium未启动'
                }

            # 检查登录状态
            if self._check_login_in_browser():
                # 获取cookies
                cookies = self.selenium_driver.get_cookies()

                # 保存cookies
                self.config_manager.save_cookies(cookies)
                self.logger.info(f"成功保存 {len(cookies)} 个cookie")

                return {
                    'success': True,
                    'message': f'登录成功，已保存 {len(cookies)} 个cookie',
                    'cookies_count': len(cookies)
                }
            else:
                return {
                    'success': False,
                    'message': '检测到未登录状态，请确保已在浏览器中完成登录'
                }

        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _check_login_in_browser(self) -> bool:
        """检查浏览器中的登录状态"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # 等待页面加载完成
            WebDriverWait(self.selenium_driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # 检查是否存在登录相关元素
            try:
                # 查找用户头像或导航栏中的用户相关元素
                user_elements = self.selenium_driver.find_elements(By.CSS_SELECTOR,
                    'a[data-gtm-value="header_user_menu"], '
                    'img[alt*="avatar"], '
                    '.user-icon, '
                    '[data-testid="header-user-menu"]'
                )

                if user_elements:
                    self.logger.info("检测到登录状态 - 找到用户元素")
                    return True

                # 检查是否存在登录按钮（如果存在说明未登录）
                login_buttons = self.selenium_driver.find_elements(By.CSS_SELECTOR,
                    'a[href*="login"], '
                    'button[data-gtm-value="header_login"]'
                )

                if login_buttons:
                    self.logger.warning("检测到登录按钮，可能未登录")
                    return False

                # 如果都没找到，尝试检查URL
                current_url = self.selenium_driver.current_url
                if 'login' in current_url:
                    self.logger.warning("当前在登录页面")
                    return False

                # 默认认为已登录
                return True

            except Exception as e:
                self.logger.error(f"检查登录元素失败: {e}")
                return False

        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False

    def _cleanup_selenium(self):
        """清理Selenium资源"""
        try:
            if self.selenium_driver:
                self.selenium_driver.quit()
                self.selenium_driver = None
                self.logger.info("Selenium资源已清理")
        except Exception as e:
            self.logger.error(f"清理Selenium资源失败: {e}")
            self.selenium_driver = None
