const { app, BrowserWindow, ipcMain, dialog } = require('electron')
const path = require('path')
const { spawn } = require('child_process')
const Store = require('electron-store')

// 配置存储
const store = new Store()

// 完全禁用硬件加速以解决GPU崩溃问题
app.disableHardwareAcceleration()

// 额外的GPU相关修复
app.commandLine.appendSwitch('--disable-gpu')
app.commandLine.appendSwitch('--disable-gpu-sandbox')
app.commandLine.appendSwitch('--disable-software-rasterizer')
app.commandLine.appendSwitch('--disable-gpu-process-crash-limit')
app.commandLine.appendSwitch('--no-sandbox')
app.commandLine.appendSwitch('--disable-features', 'VizDisplayCompositor')

// Python后端进程
let pythonProcess = null

// 主窗口
let mainWindow = null

// 开发模式检测
const isDev = process.env.NODE_ENV === 'development'

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1000,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, '../preload/index.js'),
      // 优化性能设置
      backgroundThrottling: false,
      webSecurity: false
    },
    icon: path.join(__dirname, '../renderer/assets/icon.png'),
    show: false, // 重要：初始不显示，避免闪烁
    frame: false,
    titleBarStyle: 'hidden',
    // 优化显示效果，减少闪烁
    backgroundColor: '#667eea', // 与加载页面背景色一致
    titleBarOverlay: false,
    autoHideMenuBar: true,
    // 添加平滑显示选项
    transparent: false,
    opacity: 1.0
  })

  // 先显示加载画面
  mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Pixiv Spider</title>
      <style>
        body {
          margin: 0;
          padding: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
          flex-direction: column;
        }
        .loader {
          width: 50px;
          height: 50px;
          border: 3px solid rgba(255,255,255,0.3);
          border-radius: 50%;
          border-top-color: white;
          animation: spin 1s ease-in-out infinite;
          margin-bottom: 20px;
        }
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        .title {
          font-size: 24px;
          font-weight: 300;
          margin-bottom: 10px;
        }
        .status {
          font-size: 14px;
          opacity: 0.8;
        }
      </style>
    </head>
    <body>
      <div class="loader"></div>
      <div class="title">Pixiv Spider</div>
      <div class="status">正在启动应用...</div>
    </body>
    </html>
  `))

  // 添加webContents事件监听用于调试
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('WebContents: did-finish-load')
  })

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('WebContents: did-fail-load', errorCode, errorDescription, validatedURL)
  })

  mainWindow.webContents.on('dom-ready', () => {
    console.log('WebContents: dom-ready')
  })

  // 先加载简单的加载页面
  const loadingPath = path.join(__dirname, 'loading.html')
  mainWindow.loadFile(loadingPath)

  // 窗口准备好后立即显示
  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show')
    // 平滑显示窗口
    mainWindow.show()
    mainWindow.focus()

    // 延迟一小段时间确保加载页面完全显示，然后开始加载主应用
    setTimeout(() => {
      loadMainApplication()
    }, 100)
  })

  // 窗口关闭时的处理
  mainWindow.on('closed', () => {
    mainWindow = null
    // 不在这里调用stopPythonBackend，让before-quit事件处理
  })
}

// 异步加载主应用
async function loadMainApplication() {
  try {
    console.log('Starting main application loading...')

    // 更新状态
    updateLoadingStatus('Starting backend service...')

    // 并行启动Python后端和加载前端
    const backendPromise = startPythonBackendAsync()

    // 更新状态
    updateLoadingStatus('Loading interface...')

    // 加载实际应用
    if (isDev) {
      console.log('Loading development server...')
      await mainWindow.loadURL('http://localhost:5173')
      // 开发模式下打开开发者工具
      mainWindow.webContents.openDevTools()
    } else {
      // 生产模式：加载构建后的文件
      const indexPath = path.join(__dirname, '../../dist-vue/index.html')
      console.log('Loading index file:', indexPath)

      // 检查文件是否存在
      const fs = require('fs')
      if (!fs.existsSync(indexPath)) {
        throw new Error(`Index file not found: ${indexPath}`)
      }

      // 平滑过渡到主应用
      await mainWindow.loadFile(indexPath)
      console.log('Main application loaded successfully')

      // 等待DOM完全准备好
      await new Promise(resolve => {
        if (mainWindow.webContents.isLoading()) {
          mainWindow.webContents.once('dom-ready', () => {
            // 再等待一小段时间确保Vue应用初始化
            setTimeout(resolve, 200)
          })
        } else {
          setTimeout(resolve, 200)
        }
      })

      console.log('Vue application loaded successfully')
    }

    // 等待后端启动完成（如果还没完成的话）
    await backendPromise

  } catch (error) {
    console.error('Failed to load main application:', error)
    // 显示错误页面
    await mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(`
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Startup Failed</title>
        <style>
          body {
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            text-align: center;
          }
          .error {
            color: #e74c3c;
            font-size: 18px;
            margin-bottom: 20px;
          }
          .message {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
          }
          .retry {
            margin-top: 20px;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          }
        </style>
      </head>
      <body>
        <div class="error">Application Startup Failed</div>
        <div class="message">Please check Python environment and dependencies</div>
        <div class="message">Error: ${error.message}</div>
        <button class="retry" onclick="location.reload()">Retry</button>
      </body>
      </html>
    `))
  }
}

// 更新加载状态
function updateLoadingStatus(status) {
  if (mainWindow && mainWindow.webContents) {
    console.log('Updating loading status:', status)
    mainWindow.webContents.executeJavaScript(`
      try {
        const statusEl = document.querySelector('.status');
        if (statusEl) {
          statusEl.textContent = '${status}';
        }
      } catch (e) {
        // 忽略错误，可能是页面还没加载完成
      }
    `).catch(() => {
      // 忽略错误
    })
  }
}

// IPC通信相关变量
let requestId = 0
const pendingRequests = new Map()

// 生成唯一请求ID
function generateRequestId() {
  return `req_${++requestId}_${Date.now()}`
}

// 异步启动Python后端
function startPythonBackendAsync() {
  return new Promise((resolve, reject) => {
    try {
      // 确定Python脚本路径
      let pythonPath
      let workingDir

      if (isDev) {
        // 开发模式：使用IPC服务器
        pythonPath = path.join(__dirname, '../../../run_ipc_server.py')
        workingDir = path.join(__dirname, '../../..')
      } else {
        // 生产模式：使用IPC服务器
        pythonPath = path.join(__dirname, '../../../run_ipc_server.py')
        workingDir = path.join(__dirname, '../../..')
      }

      console.log('Starting Python IPC backend:', pythonPath)

      pythonProcess = spawn('python', [pythonPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: workingDir,
        env: {
          ...process.env,
          PYTHONIOENCODING: 'utf-8',
          PYTHONLEGACYWINDOWSSTDIO: '1'
        }
      })

      let backendReady = false

      // 处理stdout - IPC消息
      pythonProcess.stdout.setEncoding('utf8')
      pythonProcess.stdout.on('data', (data) => {
        const lines = data.toString('utf8').split('\n')
        for (const line of lines) {
          const trimmedLine = line.trim()
          if (trimmedLine) {
            try {
              const message = JSON.parse(trimmedLine)
              handlePythonMessage(message)

              // 检查是否是ready事件
              if (message.type === 'event' && message.event === 'server/ready' && !backendReady) {
                backendReady = true
                console.log('Python backend ready')
                resolve()
              }
            } catch (error) {
              console.log(`Python Output: ${trimmedLine}`)
            }
          }
        }
      })

      // 处理stderr - 日志和错误
      pythonProcess.stderr.setEncoding('utf8')
      pythonProcess.stderr.on('data', (data) => {
        const logData = data.toString('utf8').trim()
        if (logData) {
          // 解析日志级别和消息
          const lines = logData.split('\n')
          for (const line of lines) {
            const trimmedLine = line.trim()
            if (trimmedLine) {
              // 检查日志级别
              if (trimmedLine.includes('ERROR -') || trimmedLine.includes('CRITICAL -')) {
                console.error(`Python Backend Error: ${trimmedLine}`)
                if (mainWindow) {
                  mainWindow.webContents.send('python-error', trimmedLine)
                }
              } else if (trimmedLine.includes('WARNING -')) {
                console.warn(`Python Backend Warning: ${trimmedLine}`)
                if (mainWindow) {
                  mainWindow.webContents.send('python-warning', trimmedLine)
                }
              } else if (trimmedLine.includes('INFO -') || trimmedLine.includes('DEBUG -')) {
                console.log(`Python Backend Info: ${trimmedLine}`)
                if (mainWindow) {
                  mainWindow.webContents.send('python-info', trimmedLine)
                }
              } else if (trimmedLine.includes('IPC server ready')) {
                // 这是正常的启动信息，不是错误
                console.log('Python backend ready')
                if (mainWindow) {
                  mainWindow.webContents.send('backend-ready')
                }
              } else {
                // 过滤掉Chrome驱动器的调试信息
                if (trimmedLine.includes('GetHandleVerifier') ||
                    trimmedLine.includes('BaseThreadInitThunk') ||
                    trimmedLine.includes('RtlInitializeExceptionChain') ||
                    trimmedLine.includes('RtlGetAppContainerNamedObjectPath') ||
                    trimmedLine.includes('Stacktrace:') ||
                    trimmedLine.includes('(Session info: chrome=') ||
                    trimmedLine.includes('(No symbol)')) {
                  // 这些是Chrome驱动器的调试信息，不是真正的错误，忽略它们
                  console.log(`Chrome Debug Info: ${trimmedLine}`)
                } else {
                  // 其他未分类的输出，可能是错误
                  console.error(`Python Backend Error: ${trimmedLine}`)
                  if (mainWindow) {
                    mainWindow.webContents.send('python-error', trimmedLine)
                  }
                }
              }
            }
          }
        }
      })

      pythonProcess.on('close', (code) => {
        console.log(`Python Backend exited with code ${code}`)
        pythonProcess = null
        // 清理所有待处理的请求
        for (const [id, { reject }] of pendingRequests) {
          reject(new Error('Python backend disconnected'))
        }
        pendingRequests.clear()

        if (!backendReady) {
          reject(new Error(`Python backend exited with code ${code}`))
        }
      })

      pythonProcess.on('error', (error) => {
        console.error('Python process error:', error)
        reject(error)
      })

      console.log('Python IPC backend started')

      // 设置超时
      setTimeout(() => {
        if (!backendReady) {
          reject(new Error('Python backend startup timeout'))
        }
      }, 10000) // 10秒超时

    } catch (error) {
      console.error('Failed to start Python backend:', error)
      reject(error)
    }
  })
}

// 保持原有的同步启动函数作为备用
function startPythonBackend() {
  startPythonBackendAsync().catch(error => {
    console.error('Backend startup failed:', error)
  })
}

// 处理来自Python的消息
function handlePythonMessage(message) {
  try {
    const { id, type, success, data, error, event } = message

    if (type === 'response') {
      // 处理响应消息
      const request = pendingRequests.get(id)
      if (request) {
        pendingRequests.delete(id)
        if (success) {
          request.resolve(data)
        } else {
          request.reject(new Error(error || 'Unknown error'))
        }
      }
    } else if (type === 'event') {
      // 处理事件消息
      handlePythonEvent(event, data)
    }
  } catch (error) {
    console.error('Error handling Python message:', error)
  }
}

// 处理Python事件
function handlePythonEvent(eventType, data) {
  if (!mainWindow) return

  switch (eventType) {
    case 'server/ready':
      // 不在这里打印，避免重复日志
      mainWindow.webContents.send('backend-ready')
      break
    case 'server/error':
      console.error('Python backend error:', data.error)
      mainWindow.webContents.send('backend-error', data.error)
      break
    case 'status_update':
      mainWindow.webContents.send('status-update', data)
      break
    case 'progress_update':
      mainWindow.webContents.send('progress-update', data)
      break
    case 'log_message':
      // 转发日志消息到前端
      mainWindow.webContents.send('log-message', data)
      break
    case 'download_complete':
      mainWindow.webContents.send('download-complete', data)
      break
    case 'auth_status':
      mainWindow.webContents.send('auth-status', data)
      break
    case 'auth/login_started':
      mainWindow.webContents.send('auth-login-started', data)
      break
    case 'auth/login_success':
      mainWindow.webContents.send('auth-login-success', data)
      break
    case 'auth/login_failed':
      mainWindow.webContents.send('auth-login-failed', data)
      break
    case 'auth/login_cancelled':
      mainWindow.webContents.send('auth-login-cancelled', data)
      break
    case 'auth/selenium_starting':
      mainWindow.webContents.send('auth-selenium-starting', data)
      break
    case 'auth/selenium_ready':
      mainWindow.webContents.send('auth-selenium-ready', data)
      break
    case 'auth/selenium_failed':
      mainWindow.webContents.send('auth-selenium-failed', data)
      break
    case 'auth/login_error':
      mainWindow.webContents.send('auth-login-error', data)
      break
    case 'download/completed':
      mainWindow.webContents.send('download-completed', data)
      break
    case 'download/failed':
      mainWindow.webContents.send('download-failed', data)
      break
    default:
      console.log('Unknown event type:', eventType, data)
  }
}

// 应用关闭标志
let isShuttingDown = false

// 发送消息到Python后端
function sendToPython(action, data = {}) {
  return new Promise((resolve, reject) => {
    // 检查应用是否正在关闭
    if (isShuttingDown) {
      reject(new Error('Application is shutting down'))
      return
    }

    if (!pythonProcess || !pythonProcess.stdin || pythonProcess.stdin.destroyed) {
      reject(new Error('Python backend not available'))
      return
    }

    const id = generateRequestId()
    const message = {
      id,
      type: 'request',
      action,
      data
    }

    // 存储待处理的请求
    pendingRequests.set(id, { resolve, reject })

    // 设置超时
    setTimeout(() => {
      if (pendingRequests.has(id)) {
        pendingRequests.delete(id)
        reject(new Error('Request timeout'))
      }
    }, 30000) // 30秒超时

    try {
      // 验证数据是否可序列化
      const jsonMessage = JSON.stringify(message) + '\n'
      console.log('Electron: 发送到Python的消息:', { action, dataKeys: Object.keys(data) })

      // 再次检查stdin状态，防止在序列化过程中被关闭
      if (pythonProcess.stdin && !pythonProcess.stdin.destroyed) {
        pythonProcess.stdin.write(jsonMessage)
      } else {
        pendingRequests.delete(id)
        reject(new Error('Python backend stdin is not available'))
      }
    } catch (error) {
      console.error('Electron: 发送消息失败:', error)
      console.error('Electron: 问题数据:', message)
      pendingRequests.delete(id)
      reject(new Error('发送消息失败: ' + error.message))
    }
  })
}

// 停止Python后端
function stopPythonBackend() {
  return new Promise((resolve) => {
    // 设置关闭标志，防止新的请求
    isShuttingDown = true

    if (pythonProcess) {
      console.log('🐍 正在停止Python后端进程...')

      // 设置超时，防止无限等待
      const timeout = setTimeout(() => {
        console.log('⚠️ Python后端停止超时，强制终止...')
        try {
          if (pythonProcess) {
            pythonProcess.kill('SIGKILL')
          }
        } catch (error) {
          console.error('强制终止Python进程失败:', error)
        }
        pythonProcess = null
        resolve()
      }, 3000) // 减少到3秒超时

      // 监听进程退出
      pythonProcess.once('exit', (code, signal) => {
        clearTimeout(timeout)
        console.log(`✅ Python后端已退出 (code: ${code}, signal: ${signal})`)
        pythonProcess = null
        resolve()
      })

      // 监听进程错误
      pythonProcess.once('error', (error) => {
        clearTimeout(timeout)
        console.error('❌ Python后端停止时出错:', error)
        pythonProcess = null
        resolve()
      })

      // 尝试优雅关闭
      try {
        // 先关闭stdin，防止写入错误
        if (pythonProcess.stdin && !pythonProcess.stdin.destroyed) {
          try {
            pythonProcess.stdin.write('{"type": "cleanup"}\n')
            pythonProcess.stdin.end()
          } catch (writeError) {
            console.log('⚠️ 写入清理信号失败，直接关闭stdin:', writeError.message)
            pythonProcess.stdin.destroy()
          }
        }

        // 等待一小段时间让Python处理清理信号
        setTimeout(() => {
          try {
            if (pythonProcess) {
              pythonProcess.kill('SIGTERM')
              console.log('📤 已发送SIGTERM信号给Python后端')
            }
          } catch (killError) {
            console.error('❌ 发送SIGTERM失败:', killError)
            clearTimeout(timeout)
            pythonProcess = null
            resolve()
          }
        }, 100) // 100ms延迟

      } catch (error) {
        console.error('❌ 发送停止信号失败:', error)
        clearTimeout(timeout)
        pythonProcess = null
        resolve()
      }
    } else {
      console.log('ℹ️ Python后端进程不存在，无需停止')
      resolve()
    }
  })
}

// 应用准备就绪
app.whenReady().then(() => {
  createWindow()
  // Python后端现在在loadMainApplication中异步启动

  // 注册快捷键
  const { globalShortcut } = require('electron')

  // F12 打开开发者工具
  globalShortcut.register('F12', () => {
    const focusedWindow = BrowserWindow.getFocusedWindow()
    if (focusedWindow) {
      focusedWindow.webContents.toggleDevTools()
    }
  })

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // 直接退出，让before-quit事件处理清理
    app.quit()
  }
})

// IPC 处理程序
ipcMain.handle('get-store-value', (event, key) => {
  return store.get(key)
})

ipcMain.handle('set-store-value', (event, key, value) => {
  store.set(key, value)
  return true
})

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options)
  return result
})

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

// 窗口控制处理程序
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize()
  }
})

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize()
    } else {
      mainWindow.maximize()
    }
  }
})

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close()
  }
})

ipcMain.handle('set-window-title', (event, title) => {
  if (mainWindow) {
    mainWindow.setTitle(title)
  }
})

// === 新的IPC API处理器 ===

// 认证相关
ipcMain.handle('api-auth-status', async () => {
  return sendToPython('auth/status')
})

ipcMain.handle('api-auth-login', async (event, forceRelogin = false) => {
  return sendToPython('auth/login', { force_relogin: forceRelogin })
})

ipcMain.handle('api-auth-check-cookies', async () => {
  return sendToPython('auth/check_cookies')
})

ipcMain.handle('api-auth-start-selenium', async () => {
  return sendToPython('auth/start_selenium')
})

ipcMain.handle('api-auth-confirm-login', async () => {
  return sendToPython('auth/confirm_login')
})

ipcMain.handle('api-auth-cancel-login', async () => {
  return sendToPython('auth/cancel_login')
})

// 下载相关
ipcMain.handle('api-download-start', async (event, settings) => {
  try {
    // 清理设置数据，确保可序列化
    const cleanSettings = JSON.parse(JSON.stringify(settings))
    console.log('Electron: 清理后的设置:', cleanSettings)

    return sendToPython('download/start', { config: cleanSettings })
  } catch (error) {
    console.error('Electron: 清理设置数据失败:', error)
    throw new Error('设置数据序列化失败: ' + error.message)
  }
})

ipcMain.handle('api-download-stop', async () => {
  return sendToPython('download/stop')
})

ipcMain.handle('api-download-pause', async () => {
  return sendToPython('download/pause')
})

ipcMain.handle('api-download-resume', async () => {
  return sendToPython('download/resume')
})

ipcMain.handle('api-download-status', async () => {
  return sendToPython('download/status')
})

// 配置相关
ipcMain.handle('api-config-get', async () => {
  return sendToPython('config/get')
})

ipcMain.handle('api-config-update', async (event, config) => {
  return sendToPython('config/update', { config })
})

// 健康检查
ipcMain.handle('api-health-check', async () => {
  return sendToPython('health')
})

// 应用退出前的清理
app.on('before-quit', async (event) => {
  // 如果已经在关闭过程中，直接退出
  if (isShuttingDown) {
    return
  }

  console.log('🧹 应用即将退出，开始清理资源...')

  // 阻止默认退出行为，等待清理完成
  event.preventDefault()
  isShuttingDown = true

  try {
    // 1. 清理所有待处理的请求
    console.log('📋 清理待处理的请求...')
    for (const [id, { reject }] of pendingRequests) {
      reject(new Error('Application is shutting down'))
    }
    pendingRequests.clear()

    // 2. 停止Python后端
    console.log('🐍 正在停止Python后端...')
    await stopPythonBackend()

    // 3. 关闭所有窗口
    console.log('🪟 关闭所有窗口...')
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.destroy()
      mainWindow = null
    }

    // 4. 清理其他资源
    console.log('🗑️ 清理其他资源...')
    // 清理定时器等

    console.log('✅ 资源清理完成，退出应用')

    // 现在可以安全退出
    app.exit(0)

  } catch (error) {
    console.error('❌ 清理资源时出错:', error)
    // 即使出错也要退出
    app.exit(1)
  }
})

// 处理强制退出信号
process.on('SIGINT', () => {
  console.log('🛑 收到SIGINT信号，强制退出...')
  isShuttingDown = true
  if (pythonProcess) {
    try {
      pythonProcess.kill('SIGKILL')
    } catch (error) {
      console.error('强制终止Python进程失败:', error)
    }
  }
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('🛑 收到SIGTERM信号，强制退出...')
  isShuttingDown = true
  if (pythonProcess) {
    try {
      pythonProcess.kill('SIGKILL')
    } catch (error) {
      console.error('强制终止Python进程失败:', error)
    }
  }
  process.exit(0)
})
