"""
用户相关的数据模型
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import datetime


@dataclass
class UserProfile:
    """用户资料数据模型"""
    id: int
    name: str
    account: str
    avatar_url: str = ""
    background_url: str = ""
    comment: str = ""
    webpage: str = ""
    twitter_account: str = ""
    total_follow_users: int = 0
    total_my_pixiv_users: int = 0
    total_illusts: int = 0
    total_manga: int = 0
    total_novels: int = 0
    total_illust_bookmarks_public: int = 0
    is_followed: bool = False
    is_access_blocking_user: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def url(self) -> str:
        """获取用户URL"""
        return f"https://www.pixiv.net/users/{self.id}"
    
    @property
    def total_artworks(self) -> int:
        """获取总作品数"""
        return self.total_illusts + self.total_manga
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'account': self.account,
            'avatar_url': self.avatar_url,
            'background_url': self.background_url,
            'comment': self.comment,
            'webpage': self.webpage,
            'twitter_account': self.twitter_account,
            'total_follow_users': self.total_follow_users,
            'total_my_pixiv_users': self.total_my_pixiv_users,
            'total_illusts': self.total_illusts,
            'total_manga': self.total_manga,
            'total_novels': self.total_novels,
            'total_illust_bookmarks_public': self.total_illust_bookmarks_public,
            'is_followed': self.is_followed,
            'is_access_blocking_user': self.is_access_blocking_user,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserProfile':
        """从字典创建实例"""
        return cls(
            id=data['id'],
            name=data['name'],
            account=data['account'],
            avatar_url=data.get('avatar_url', ''),
            background_url=data.get('background_url', ''),
            comment=data.get('comment', ''),
            webpage=data.get('webpage', ''),
            twitter_account=data.get('twitter_account', ''),
            total_follow_users=data.get('total_follow_users', 0),
            total_my_pixiv_users=data.get('total_my_pixiv_users', 0),
            total_illusts=data.get('total_illusts', 0),
            total_manga=data.get('total_manga', 0),
            total_novels=data.get('total_novels', 0),
            total_illust_bookmarks_public=data.get('total_illust_bookmarks_public', 0),
            is_followed=data.get('is_followed', False),
            is_access_blocking_user=data.get('is_access_blocking_user', False),
            metadata=data.get('metadata', {})
        )


@dataclass  
class User:
    """简化的用户数据模型"""
    id: int
    name: str
    account: str = ""
    avatar_url: str = ""
    profile: Optional[UserProfile] = None
    
    @property
    def url(self) -> str:
        """获取用户URL"""
        return f"https://www.pixiv.net/users/{self.id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'account': self.account,
            'avatar_url': self.avatar_url,
            'profile': self.profile.to_dict() if self.profile else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        """从字典创建实例"""
        user = cls(
            id=data['id'],
            name=data['name'],
            account=data.get('account', ''),
            avatar_url=data.get('avatar_url', '')
        )
        
        if data.get('profile'):
            user.profile = UserProfile.from_dict(data['profile'])
        
        return user 