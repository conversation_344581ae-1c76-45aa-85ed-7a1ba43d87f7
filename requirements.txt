# 核心依赖
requests>=2.25.0
selenium>=4.0.0
webdriver-manager>=4.0.0
beautifulsoup4>=4.9.0
lxml>=4.6.0
pillow>=8.0.0

# 数据处理
numpy>=1.20.0
pandas>=1.3.0

# 网络相关
urllib3>=1.26.0
certifi>=2021.0.0

# 系统相关
psutil>=5.8.0
pathlib2>=2.3.0; python_version<"3.4"

# 日志相关
colorlog>=6.0.0

# API服务器依赖
fastapi>=0.104.0
uvicorn>=0.24.0
websockets>=11.0.0
pydantic>=2.4.0

# GUI依赖（可选）
# tkinter 通常是Python内置的

# 开发依赖（仅开发时需要）
# pytest>=6.0.0
# pytest-cov>=2.10.0
# black>=21.0.0
# flake8>=3.8.0
# mypy>=0.812 