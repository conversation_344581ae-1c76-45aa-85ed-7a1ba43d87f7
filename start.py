#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pixiv Spider v6.0 一键启动器

智能检测环境并启动应用程序
"""

import os
import sys
import subprocess
import platform
import time
from pathlib import Path

class PixivSpiderLauncher:
    """Pixiv Spider 启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.electron_dir = self.project_root / "electron-gui"
        self.python_executable = sys.executable
        
    def print_banner(self):
        """显示启动横幅"""
        print("=" * 60)
        print("🎨 Pixiv Spider v6.0 - 现代化桌面爬虫工具")
        print("=" * 60)
        print("⚡ 闪电启动 | 🎨 现代界面 | 🛡️ 稳定可靠")
        print()
    
    def check_environment(self):
        """统一检查环境 - 合并重复的检查逻辑"""
        print("🔍 检查运行环境...")

        # 检查Python版本
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python版本过低，需要Python 3.8+")
            print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
            return False, "python"
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")

        # 检查Node.js版本
        try:
            result = subprocess.run(['node', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                node_version = result.stdout.strip()
                print(f"✅ Node.js版本: {node_version}")
            else:
                print("❌ Node.js未安装或版本过低")
                return False, "node"
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ Node.js未安装")
            return False, "node"

        # 检查Python依赖
        try:
            import requests
            import selenium
            print("✅ 核心依赖已安装")
        except ImportError as e:
            print(f"❌ 缺少依赖: {e}")
            print("💡 请运行: pip install -r requirements.txt")
            return False, "dependencies"

        return True, "success"
    
    # check_dependencies方法已合并到check_environment中
    
    def check_electron_build(self):
        """检查Electron构建"""
        print("🔧 检查前端构建...")
        dist_vue = self.electron_dir / "dist-vue"
        if not dist_vue.exists() or not list(dist_vue.glob("*.html")):
            print("❌ 前端未构建，正在构建...")
            return self.build_electron()
        print("✅ 前端构建完成")
        return True
    
    def build_electron(self):
        """构建Electron前端"""
        if not self.electron_dir.exists():
            print("❌ electron-gui目录不存在")
            return False
        
        print("🔨 正在构建前端...")
        try:
            # 检查node_modules
            node_modules = self.electron_dir / "node_modules"
            if not node_modules.exists():
                print("📦 安装Node.js依赖...")
                result = subprocess.run(['npm', 'install'],
                                      cwd=self.electron_dir,
                                      capture_output=True, text=True,
                                      shell=platform.system() == "Windows")
                if result.returncode != 0:
                    print("❌ npm install失败")
                    print(result.stderr)
                    return False
            
            # 构建Vue应用
            print("🏗️  构建Vue应用...")
            result = subprocess.run(['npm', 'run', 'build'],
                                  cwd=self.electron_dir,
                                  capture_output=True, text=True,
                                  shell=platform.system() == "Windows")
            if result.returncode != 0:
                print("❌ 构建失败")
                print(result.stderr)
                return False
            
            print("✅ 构建完成")
            return True
            
        except Exception as e:
            print(f"❌ 构建过程出错: {e}")
            return False
    
    def start_application(self):
        """启动应用程序"""
        print("\n🚀 启动Pixiv Spider...")

        try:
            # 启动Electron应用
            if platform.system() == "Windows":
                # Windows需要使用shell=True
                subprocess.Popen(['npm', 'run', 'app'],
                               cwd=self.electron_dir,
                               shell=True,
                               creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                subprocess.Popen(['npm', 'run', 'app'],
                               cwd=self.electron_dir)

            print("✅ 应用程序已启动！")
            print("\n💡 提示:")
            print("   - 如果窗口没有出现，请稍等几秒")
            print("   - 首次启动可能需要更长时间")
            print("   - 关闭此窗口不会影响应用运行")

            return True

        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    def show_help(self):
        """显示帮助信息"""
        print("\n📖 使用说明:")
        print("   1. 确保已安装Python 3.8+和Node.js 16+")
        print("   2. 运行此脚本自动检查环境并启动应用")
        print("   3. 首次运行会自动安装依赖和构建前端")
        print("\n🔧 手动启动:")
        print("   cd electron-gui && npm run app")
        print("\n🆘 遇到问题:")
        print("   - 运行 python cleanup_project.py 清理项目")
        print("   - 检查网络连接")
        print("   - 查看README.md获取详细说明")
    
    def run(self):
        """运行启动器 - 使用优化后的统一检查"""
        self.print_banner()

        # 统一环境检查
        env_ok, env_error = self.check_environment()
        if not env_ok:
            if env_error == "node":
                print("\n💡 请安装Node.js 16+: https://nodejs.org/")
            self.show_help()
            return False

        # 检查前端构建
        if not self.check_electron_build():
            self.show_help()
            return False

        # 启动应用
        if self.start_application():
            print("\n🎉 启动成功！享受使用Pixiv Spider v6.0！")
            return True
        else:
            self.show_help()
            return False

def main():
    """主函数"""
    launcher = PixivSpiderLauncher()
    
    try:
        success = launcher.run()
        if not success:
            input("\n按Enter键退出...")
    except KeyboardInterrupt:
        print("\n\n👋 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动器出错: {e}")
        input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
