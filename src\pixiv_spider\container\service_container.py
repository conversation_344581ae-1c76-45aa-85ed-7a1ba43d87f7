"""
服务容器

实现依赖注入和服务管理
"""

import logging
from typing import Dict, Any, Type, TypeVar, Optional, Callable
from ..config.config_manager import ConfigManager

T = TypeVar('T')


class ServiceContainer:
    """服务容器类"""
    
    def __init__(self):
        """初始化服务容器"""
        self._services: Dict[str, Any] = {}
        self._singletons: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self.logger = logging.getLogger(__name__)
        
        # 注册配置管理器作为默认服务
        self._singletons['config_manager'] = ConfigManager()
    
    def register_singleton(self, name: str, instance: Any) -> None:
        """
        注册单例服务
        
        Args:
            name: 服务名称
            instance: 服务实例
        """
        self._singletons[name] = instance
        self.logger.debug(f"注册单例服务: {name}")
    
    def register_factory(self, name: str, factory: Callable) -> None:
        """
        注册工厂方法
        
        Args:
            name: 服务名称
            factory: 工厂方法
        """
        self._factories[name] = factory
        self.logger.debug(f"注册工厂服务: {name}")
    
    def register_transient(self, name: str, service_class: Type[T]) -> None:
        """
        注册瞬态服务
        
        Args:
            name: 服务名称
            service_class: 服务类
        """
        self._services[name] = service_class
        self.logger.debug(f"注册瞬态服务: {name}")
    
    def get(self, name: str) -> Any:
        """
        获取服务实例
        
        Args:
            name: 服务名称
            
        Returns:
            Any: 服务实例
        """
        # 检查单例
        if name in self._singletons:
            return self._singletons[name]
        
        # 检查工厂方法
        if name in self._factories:
            return self._factories[name](self)
        
        # 检查瞬态服务
        if name in self._services:
            service_class = self._services[name]
            return self._create_instance(service_class)
        
        raise ValueError(f"服务未注册: {name}")
    
    def get_config_manager(self) -> ConfigManager:
        """获取配置管理器"""
        return self.get('config_manager')

    def register_config_manager(self, config_manager: ConfigManager) -> None:
        """注册配置管理器实例，确保所有服务使用同一实例"""
        self._singletons['config_manager'] = config_manager
    
    def _create_instance(self, service_class: Type[T]) -> T:
        """
        创建服务实例
        
        Args:
            service_class: 服务类
            
        Returns:
            T: 服务实例
        """
        try:
            # 尝试使用配置管理器创建实例
            config_manager = self.get_config_manager()
            return service_class(config_manager)
        except TypeError:
            try:
                # 尝试无参数创建
                return service_class()
            except Exception as e:
                self.logger.error(f"创建服务实例失败: {service_class.__name__}, 错误: {e}")
                raise
    
    def has(self, name: str) -> bool:
        """
        检查服务是否已注册
        
        Args:
            name: 服务名称
            
        Returns:
            bool: 是否已注册
        """
        return (name in self._singletons or 
                name in self._factories or 
                name in self._services)
    
    def clear(self) -> None:
        """清空所有服务"""
        self._services.clear()
        self._singletons.clear()
        self._factories.clear()
        self.logger.info("服务容器已清空")
