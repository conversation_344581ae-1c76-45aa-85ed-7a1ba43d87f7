"""
服务注册器

负责注册系统中的所有服务
"""

import logging
from .service_container import ServiceContainer
from ..services.auth_service import AuthService
from ..services.pixiv_api_service import PixivApiService
from ..services.download_service import DownloadService
from ..services.cache_service import CacheService



class ServiceRegistry:
    """服务注册器"""
    
    def __init__(self, container: ServiceContainer):
        """
        初始化服务注册器
        
        Args:
            container: 服务容器
        """
        self.container = container
        self.logger = logging.getLogger(__name__)
    
    def register_all_services(self) -> None:
        """注册所有服务"""
        self._register_core_services()
        self._register_utility_services()
        self.logger.info("所有服务已注册完成")
    
    def _register_core_services(self) -> None:
        """注册核心服务"""
        # 认证服务（单例）
        self.container.register_factory('auth_service', self._create_auth_service)
        
        # API服务（工厂方法，需要cookies）
        self.container.register_factory('api_service', self._create_api_service)
        
        # 下载服务（工厂方法，需要API服务）
        self.container.register_factory('download_service', self._create_download_service)
        
        self.logger.debug("核心服务注册完成")
    
    def _register_utility_services(self) -> None:
        """注册工具服务"""
        # 缓存服务（单例）
        self.container.register_transient('cache_service', CacheService)
        
        self.logger.debug("工具服务注册完成")
    
    def _create_auth_service(self, container: ServiceContainer) -> AuthService:
        """创建认证服务"""
        config_manager = container.get_config_manager()
        return AuthService(config_manager)
    
    def _create_api_service(self, container: ServiceContainer) -> PixivApiService:
        """创建API服务"""
        # 这里需要cookies，通常在运行时提供
        # 返回一个工厂函数，实际创建时需要cookies
        def api_service_factory(cookies):
            config_manager = container.get_config_manager()
            return PixivApiService(cookies, config_manager)
        return api_service_factory
    
    def _create_download_service(self, container: ServiceContainer) -> DownloadService:
        """创建下载服务"""
        # 这里需要API服务，通常在运行时提供
        def download_service_factory(api_service):
            config_manager = container.get_config_manager()
            return DownloadService(api_service, config_manager)
        return download_service_factory
    
    def create_configured_api_service(self, cookies) -> PixivApiService:
        """
        创建配置好的API服务
        
        Args:
            cookies: 认证cookies
            
        Returns:
            PixivApiService: API服务实例
        """
        factory = self.container.get('api_service')
        return factory(cookies)
    
    def create_configured_download_service(self, api_service) -> DownloadService:
        """
        创建配置好的下载服务
        
        Args:
            api_service: API服务实例
            
        Returns:
            DownloadService: 下载服务实例
        """
        factory = self.container.get('download_service')
        return factory(api_service)
