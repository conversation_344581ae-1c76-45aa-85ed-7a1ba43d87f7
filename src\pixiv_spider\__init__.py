"""
Pixiv Spider - 一个模块化的Pixiv作品爬虫工具

这是一个重构后的Pixiv爬虫，采用了标准的Python包结构和设计模式。

主要特性：
- 模块化架构，易于维护和扩展
- 支持多种下载模式
- 智能重试和错误处理
- 现代化的GUI界面
- 高性能并发下载
"""

__version__ = "3.0.0"
__author__ = "Pixiv Spider Team"
__license__ = "MIT"

# 版本信息
VERSION_INFO = {
    "major": 3,
    "minor": 0,
    "patch": 0,
    "pre_release": None
}

# 主要组件导入
from .core import PixivSpider
from .models import Artwork, User, DownloadConfig
from .services import DownloadService, AuthService
from .controllers import SpiderController
from .container import ServiceContainer, ServiceRegistry
from .processors import ArtworkProcessor

__all__ = [
    "__version__",
    "VERSION_INFO",
    "PixivSpider",
    "Artwork",
    "User",
    "DownloadConfig",
    "DownloadService",
    "AuthService",
    "SpiderController",
    "ServiceContainer",
    "ServiceRegistry",
    "ArtworkProcessor"
]