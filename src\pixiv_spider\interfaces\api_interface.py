"""
API服务接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List


class IApiService(ABC):
    """API服务接口"""
    
    @abstractmethod
    def get_artwork_detail(self, artwork_id: int) -> Optional[Dict[str, Any]]:
        """
        获取作品详情
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            Optional[Dict]: 作品详情数据
        """
        pass
    
    @abstractmethod
    def get_artwork_pages(self, artwork_id: int) -> Optional[Dict[str, Any]]:
        """
        获取作品页面信息
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            Optional[Dict]: 页面信息数据
        """
        pass
    
    @abstractmethod
    def get_artwork_details_batch(self, artwork_ids: List[int], max_workers: int = None) -> Dict[int, Optional[Dict[str, Any]]]:
        """
        批量获取作品详情
        
        Args:
            artwork_ids: 作品ID列表
            max_workers: 最大并发数
            
        Returns:
            Dict[int, Optional[Dict]]: 作品ID到详情数据的映射
        """
        pass
    
    @abstractmethod
    def get_user_artworks(self, user_id: int, page: int = 1) -> Optional[Dict[str, Any]]:
        """
        获取用户作品列表
        
        Args:
            user_id: 用户ID
            page: 页码
            
        Returns:
            Optional[Dict]: 用户作品数据
        """
        pass
    
    @abstractmethod
    def make_request(self, url: str, method: str = 'GET', **kwargs) -> Optional[Any]:
        """
        通用请求方法
        
        Args:
            url: 请求URL
            method: 请求方法
            **kwargs: 其他请求参数
            
        Returns:
            Optional[Any]: 响应对象
        """
        pass
