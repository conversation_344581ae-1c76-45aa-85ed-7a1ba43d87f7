"""
统一日志格式化工具

标准化日志记录格式，减少重复的日志输出模式
"""

import logging
import time
from typing import Any, Dict, Optional


class LogFormatter:
    """统一的日志格式化器"""
    
    # 标准化的日志前缀
    PREFIXES = {
        'start': '🚀',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️',
        'debug': '🔧',
        'progress': '📊',
        'network': '🌐',
        'file': '📁',
        'download': '📥',
        'upload': '📤',
        'cache': '📦',
        'config': '⚙️',
        'auth': '🔐',
        'cleanup': '🧹',
        'time': '⏱️'
    }
    
    @classmethod
    def format_start(cls, operation: str, details: str = "") -> str:
        """格式化开始操作的日志"""
        if details:
            return f"{cls.PREFIXES['start']} 开始{operation}: {details}"
        return f"{cls.PREFIXES['start']} 开始{operation}..."
    
    @classmethod
    def format_success(cls, operation: str, details: str = "") -> str:
        """格式化成功操作的日志"""
        if details:
            return f"{cls.PREFIXES['success']} {operation}成功: {details}"
        return f"{cls.PREFIXES['success']} {operation}成功"
    
    @classmethod
    def format_error(cls, operation: str, error: str) -> str:
        """格式化错误日志"""
        return f"{cls.PREFIXES['error']} {operation}失败: {error}"
    
    @classmethod
    def format_warning(cls, message: str) -> str:
        """格式化警告日志"""
        return f"{cls.PREFIXES['warning']} {message}"
    
    @classmethod
    def format_progress(cls, current: int, total: int, operation: str = "") -> str:
        """格式化进度日志"""
        percentage = (current / total * 100) if total > 0 else 0
        if operation:
            return f"{cls.PREFIXES['progress']} {operation}: {current}/{total} ({percentage:.1f}%)"
        return f"{cls.PREFIXES['progress']} 进度: {current}/{total} ({percentage:.1f}%)"
    
    @classmethod
    def format_time_taken(cls, operation: str, start_time: float) -> str:
        """格式化耗时日志"""
        elapsed = time.time() - start_time
        return f"{cls.PREFIXES['time']} {operation}耗时: {elapsed:.2f}秒"
    
    @classmethod
    def format_network(cls, action: str, url: str = "", status: str = "") -> str:
        """格式化网络操作日志"""
        if url and status:
            return f"{cls.PREFIXES['network']} {action}: {url} ({status})"
        elif url:
            return f"{cls.PREFIXES['network']} {action}: {url}"
        return f"{cls.PREFIXES['network']} {action}"
    
    @classmethod
    def format_file_operation(cls, action: str, path: str, details: str = "") -> str:
        """格式化文件操作日志"""
        if details:
            return f"{cls.PREFIXES['file']} {action}: {path} ({details})"
        return f"{cls.PREFIXES['file']} {action}: {path}"
    
    @classmethod
    def format_download(cls, action: str, item: str, details: str = "") -> str:
        """格式化下载日志"""
        if details:
            return f"{cls.PREFIXES['download']} {action}: {item} ({details})"
        return f"{cls.PREFIXES['download']} {action}: {item}"
    
    @classmethod
    def format_cache_operation(cls, action: str, details: str = "") -> str:
        """格式化缓存操作日志"""
        if details:
            return f"{cls.PREFIXES['cache']} 缓存{action}: {details}"
        return f"{cls.PREFIXES['cache']} 缓存{action}"
    
    @classmethod
    def format_config(cls, action: str, details: str = "") -> str:
        """格式化配置日志"""
        if details:
            return f"{cls.PREFIXES['config']} 配置{action}: {details}"
        return f"{cls.PREFIXES['config']} 配置{action}"
    
    @classmethod
    def format_auth(cls, action: str, details: str = "") -> str:
        """格式化认证日志"""
        if details:
            return f"{cls.PREFIXES['auth']} 认证{action}: {details}"
        return f"{cls.PREFIXES['auth']} 认证{action}"
    
    @classmethod
    def format_cleanup(cls, action: str, details: str = "") -> str:
        """格式化清理日志"""
        if details:
            return f"{cls.PREFIXES['cleanup']} 清理{action}: {details}"
        return f"{cls.PREFIXES['cleanup']} 清理{action}"


class StandardLogger:
    """标准化的日志记录器包装类"""
    
    def __init__(self, logger: logging.Logger):
        """
        初始化标准日志记录器
        
        Args:
            logger: Python标准日志记录器
        """
        self.logger = logger
        self.formatter = LogFormatter()
    
    def log_start(self, operation: str, details: str = "", level: int = logging.INFO):
        """记录开始操作"""
        message = self.formatter.format_start(operation, details)
        self.logger.log(level, message)
    
    def log_success(self, operation: str, details: str = "", level: int = logging.INFO):
        """记录成功操作"""
        message = self.formatter.format_success(operation, details)
        self.logger.log(level, message)
    
    def log_error(self, operation: str, error: str, level: int = logging.ERROR):
        """记录错误"""
        message = self.formatter.format_error(operation, error)
        self.logger.log(level, message)
    
    def log_warning(self, message: str):
        """记录警告"""
        formatted_message = self.formatter.format_warning(message)
        self.logger.warning(formatted_message)
    
    def log_progress(self, current: int, total: int, operation: str = "", level: int = logging.INFO):
        """记录进度"""
        message = self.formatter.format_progress(current, total, operation)
        self.logger.log(level, message)
    
    def log_time_taken(self, operation: str, start_time: float, level: int = logging.INFO):
        """记录耗时"""
        message = self.formatter.format_time_taken(operation, start_time)
        self.logger.log(level, message)
    
    def log_network(self, action: str, url: str = "", status: str = "", level: int = logging.INFO):
        """记录网络操作"""
        message = self.formatter.format_network(action, url, status)
        self.logger.log(level, message)
    
    def log_file_operation(self, action: str, path: str, details: str = "", level: int = logging.INFO):
        """记录文件操作"""
        message = self.formatter.format_file_operation(action, path, details)
        self.logger.log(level, message)
    
    def log_download(self, action: str, item: str, details: str = "", level: int = logging.INFO):
        """记录下载操作"""
        message = self.formatter.format_download(action, item, details)
        self.logger.log(level, message)
    
    def log_cache_operation(self, action: str, details: str = "", level: int = logging.INFO):
        """记录缓存操作"""
        message = self.formatter.format_cache_operation(action, details)
        self.logger.log(level, message)
    
    def log_config(self, action: str, details: str = "", level: int = logging.INFO):
        """记录配置操作"""
        message = self.formatter.format_config(action, details)
        self.logger.log(level, message)
    
    def log_auth(self, action: str, details: str = "", level: int = logging.INFO):
        """记录认证操作"""
        message = self.formatter.format_auth(action, details)
        self.logger.log(level, message)
    
    def log_cleanup(self, action: str, details: str = "", level: int = logging.INFO):
        """记录清理操作"""
        message = self.formatter.format_cleanup(action, details)
        self.logger.log(level, message)


def get_standard_logger(name: str) -> StandardLogger:
    """
    获取标准化的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        StandardLogger: 标准化的日志记录器
    """
    logger = logging.getLogger(name)
    return StandardLogger(logger)


# 全局日志格式化器实例
log_formatter = LogFormatter()
