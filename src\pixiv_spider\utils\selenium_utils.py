"""
Selenium工具类

封装Selenium WebDriver的常用操作
"""

import os
import re
import time
import logging
from typing import List, Set, Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

from ..models.exceptions import SeleniumError
from ..config.config_manager import ConfigManager


class SeleniumDriver:
    """Selenium驱动器工具类"""

    # 统一的选择器配置
    SELECTORS = {
        'artwork_links': [
            'a[href*="/artworks/"]',
            'a[href^="/artworks/"]',
            '.gtm-new-work-thumbnail-click',
            '[data-gtm-value*="artworks"]'
        ],
        'user_menu': [
            'a[data-gtm-value="header_user_menu"]',
            'img[alt*="avatar"]',
            '.user-icon',
            '[data-testid="header-user-menu"]',
            '.header-menu'
        ],
        'login_buttons': [
            'a[href*="login"]',
            'button[data-gtm-value="header_login"]',
            '.signup-form'
        ]
    }

    def __init__(self, cookies: List[Dict[str, Any]], config_manager: Optional[ConfigManager] = None):
        """
        初始化Selenium驱动器
        
        Args:
            cookies: 认证cookies
            config_manager: 配置管理器
        """
        self.cookies = cookies
        self.config_manager = config_manager or ConfigManager()
        self.spider_config = self.config_manager.load_spider_config()
        self.logger = logging.getLogger(__name__)

        self.driver: Optional[webdriver.Chrome] = None

        # 页面链接缓存
        self._page_cache = {}
        self._cache_max_size = 100  # 最大缓存页面数

        # 进程ID跟踪 - 只清理爬虫控制的进程
        self._driver_pid = None  # ChromeDriver进程ID
        self._browser_pid = None  # Chrome浏览器进程ID

        self._setup_driver()
    
    def _setup_driver(self) -> None:
        """设置驱动器 - 简化配置以提高稳定性"""
        try:
            options = Options()

            # 基本必要选项
            if self.spider_config.selenium_headless:
                options.add_argument('--headless')

            # 核心稳定性选项
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--window-size=1920,1080')

            # 减少自动化检测
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 基本日志控制
            options.add_argument('--log-level=3')

            # 通知和弹窗控制
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2  # 禁用通知
                }
            }
            options.add_experimental_option("prefs", prefs)

            # 用户代理
            options.add_argument(f'--user-agent={self.spider_config.user_agent}')

            # 代理设置
            if self.spider_config.proxy:
                options.add_argument(f'--proxy-server={self.spider_config.proxy}')

            # 创建Chrome服务，使用webdriver-manager自动管理驱动器
            try:
                from webdriver_manager.chrome import ChromeDriverManager
                service = Service(ChromeDriverManager().install())
                service.log_path = os.devnull if hasattr(os, 'devnull') else 'NUL'  # Windows使用NUL，Unix使用/dev/null
                self.logger.info("使用webdriver-manager自动管理Chrome驱动器")
            except ImportError:
                self.logger.warning("webdriver-manager未安装，使用系统PATH中的chromedriver")
                service = Service()
                service.log_path = os.devnull if hasattr(os, 'devnull') else 'NUL'

            self.driver = webdriver.Chrome(service=service, options=options)

            # 记录进程ID，用于精确清理
            try:
                # 记录ChromeDriver进程ID
                if hasattr(service, 'process') and service.process:
                    self._driver_pid = service.process.pid
                    self.logger.info(f"记录ChromeDriver进程ID: {self._driver_pid}")

                # 记录Chrome浏览器进程ID
                if hasattr(self.driver, 'service') and hasattr(self.driver.service, 'process'):
                    self._driver_pid = self.driver.service.process.pid
                    self.logger.info(f"记录ChromeDriver进程ID: {self._driver_pid}")

                # 获取Chrome浏览器进程ID（通过WebDriver的capabilities）
                try:
                    import psutil
                    # 查找与ChromeDriver关联的Chrome进程
                    for proc in psutil.process_iter(['pid', 'name', 'ppid']):
                        if (proc.info['name'] in ['chrome.exe', 'chrome'] and
                            proc.info['ppid'] == self._driver_pid):
                            self._browser_pid = proc.info['pid']
                            self.logger.info(f"记录Chrome浏览器进程ID: {self._browser_pid}")
                            break
                except ImportError:
                    self.logger.debug("psutil未安装，无法精确跟踪浏览器进程")
                except Exception as e:
                    self.logger.debug(f"获取浏览器进程ID失败: {e}")

            except Exception as e:
                self.logger.debug(f"记录进程ID失败: {e}")

            # 设置超时
            self.driver.set_page_load_timeout(self.spider_config.selenium_page_load_timeout)
            self.driver.implicitly_wait(self.spider_config.selenium_timeout)

            # 执行脚本隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置cookies
            self._set_cookies()

            self.logger.info("Selenium驱动器初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化Selenium驱动器失败: {e}")
            raise SeleniumError(f"初始化驱动器失败: {e}")
    
    def _set_cookies(self) -> None:
        """设置cookies - 健壮版本"""
        if not self.cookies:
            self.logger.warning("没有cookies可设置")
            return

        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试设置cookies (第{attempt + 1}次)")

                # 设置较短的超时时间
                original_timeout = self.spider_config.selenium_page_load_timeout

                try:
                    self.driver.set_page_load_timeout(20)  # 增加超时时间

                    # 访问pixiv主页
                    self.logger.debug("正在访问Pixiv主页...")
                    self.driver.get('https://www.pixiv.net/')
                    time.sleep(2)  # 增加等待时间确保页面加载完成

                    # 检查页面是否正确加载
                    if "pixiv" not in self.driver.title.lower():
                        raise Exception(f"页面加载异常，标题: {self.driver.title}")

                    # 删除现有cookies
                    self.driver.delete_all_cookies()

                    # 添加cookies
                    success_count = 0
                    for cookie in self.cookies:
                        try:
                            # 清理不必要的字段
                            clean_cookie = {
                                'name': cookie['name'],
                                'value': cookie['value'],
                                'domain': cookie.get('domain', '.pixiv.net'),
                                'path': cookie.get('path', '/'),
                            }

                            # 处理安全属性
                            if 'secure' in cookie and cookie['secure']:
                                clean_cookie['secure'] = True
                            if 'httpOnly' in cookie and cookie['httpOnly']:
                                clean_cookie['httpOnly'] = True

                            self.driver.add_cookie(clean_cookie)
                            success_count += 1

                        except Exception as e:
                            self.logger.debug(f"跳过cookie {cookie.get('name', 'unknown')}: {e}")
                            continue

                    # 轻量刷新
                    if success_count > 0:
                        self.driver.refresh()
                        time.sleep(2)

                    self.logger.info(f"成功设置 {success_count}/{len(self.cookies)} 个cookie")

                    # 如果设置了一些cookie，认为是成功的
                    if success_count > 0:
                        return  # 成功设置，退出重试循环
                    else:
                        raise Exception("没有成功设置任何cookie")

                finally:
                    # 恢复原始超时
                    try:
                        self.driver.set_page_load_timeout(original_timeout)
                    except:
                        pass

            except Exception as e:
                self.logger.warning(f"第{attempt + 1}次设置cookies失败: {e}")
                if attempt == max_retries - 1:
                    self.logger.warning("所有重试均失败，将在无cookie状态下继续运行")
                    return  # 不抛出异常，允许程序继续运行
                time.sleep(3)  # 增加重试间隔
    
    def get_page_links_batch(self, page_urls: List[str], max_concurrent: int = 3, fast_mode: bool = True) -> Set[str]:
        """
        批量获取页面链接（超级优化版本）

        Args:
            page_urls: 页面URL列表
            max_concurrent: 最大并发数（考虑到Selenium的限制，不宜过高）
            fast_mode: 是否使用快速模式

        Returns:
            Set[str]: 作品链接集合
        """
        if not page_urls:
            return set()

        all_links = set()

        # 如果只有少量页面，直接串行处理
        if len(page_urls) <= 2:
            self.logger.info(f"页面数量较少({len(page_urls)})，使用串行处理")
            for url in page_urls:
                links = self.get_single_page_links_optimized(url, fast_mode)
                all_links.update(links)
                if len(page_urls) > 1:
                    time.sleep(0.2)  # 极短延迟
            return all_links

        # 分批处理，避免过多并发
        batch_size = max_concurrent
        total_batches = (len(page_urls) + batch_size - 1) // batch_size

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(page_urls))
            batch_urls = page_urls[start_idx:end_idx]

            self.logger.info(f"快速处理批次 {batch_idx + 1}/{total_batches}: {len(batch_urls)} 个页面")

            # 处理当前批次
            batch_links = self._process_url_batch_fast(batch_urls, fast_mode)
            all_links.update(batch_links)

            # 批次间延迟 - 恢复稳定的等待时间
            if batch_idx < total_batches - 1:
                time.sleep(1.5)  # 恢复到稳定的1.5秒

        self.logger.info(f"快速批量获取完成，共找到 {len(all_links)} 个作品链接")
        return all_links

    def _process_url_batch(self, urls: List[str]) -> Set[str]:
        """
        处理一批URL（串行处理，但优化了单页处理逻辑）

        Args:
            urls: URL列表

        Returns:
            Set[str]: 作品链接集合
        """
        return self._process_url_batch_fast(urls, fast_mode=False)

    def _process_url_batch_fast(self, urls: List[str], fast_mode: bool = True) -> Set[str]:
        """
        快速处理一批URL

        Args:
            urls: URL列表
            fast_mode: 是否使用快速模式

        Returns:
            Set[str]: 作品链接集合
        """
        batch_links = set()

        for i, url in enumerate(urls):
            try:
                self.logger.debug(f"快速处理页面 {i+1}/{len(urls)}: {url}")
                links = self.get_single_page_links_optimized(url, fast_mode)
                batch_links.update(links)

                # 页面间延迟 - 恢复稳定的等待时间
                if i < len(urls) - 1:
                    time.sleep(0.8 if fast_mode else 1.2)  # 恢复到稳定的等待时间

            except Exception as e:
                self.logger.error(f"获取页面链接失败: {url}, 错误: {e}")
                continue

        return batch_links

    def _wait_for_page_ready(self, timeout: int = 10) -> bool:
        """
        稳定等待页面准备就绪

        Args:
            timeout: 超时时间（恢复到10秒）

        Returns:
            bool: 页面是否准备就绪
        """
        try:
            # 等待页面基本加载完成
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )

            # 检查作品容器是否出现
            selectors_to_wait = [
                'a[href*="/artworks/"]',  # 最重要：作品链接
                'section[role="main"]',  # 主要内容区域
                '[data-gtm-value*="artworks"]',  # 作品相关元素
            ]

            for selector in selectors_to_wait:
                try:
                    WebDriverWait(self.driver, 3).until(  # 恢复到3秒
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    return True
                except TimeoutException:
                    continue

            # 如果没有找到预期元素，等待后继续
            time.sleep(1.5)  # 恢复到1.5秒
            return True

        except Exception as e:
            self.logger.warning(f"等待页面就绪失败: {e}")
            return False
    
    def get_single_page_links(self, url: str) -> Set[str]:
        """
        获取单个页面的作品链接（保持向后兼容）

        Args:
            url: 页面URL

        Returns:
            Set[str]: 作品链接集合
        """
        return self.get_single_page_links_optimized(url)

    def get_single_page_links_optimized(self, url: str, fast_mode: bool = True) -> Set[str]:
        """
        获取单个页面的作品链接（优化版本，带缓存）- 超详细调试版本

        Args:
            url: 页面URL
            fast_mode: 是否使用快速模式

        Returns:
            Set[str]: 作品链接集合
        """
        self.logger.info(f"🚀 开始处理页面: {url}")
        self.logger.info(f"⚙️ 快速模式: {fast_mode}")

        # 检查缓存
        if url in self._page_cache:
            cached_links = self._page_cache[url].copy()
            self.logger.info(f"💾 使用缓存的页面链接: {len(cached_links)} 个")
            return cached_links

        start_time = time.time()

        try:
            # 加载页面
            self.logger.info(f"🌐 正在加载页面: {url}")
            load_start = time.time()
            self.driver.get(url)
            load_time = time.time() - load_start
            self.logger.info(f"✅ 页面加载请求已发送 (耗时: {load_time:.2f}秒)")

            # 检查页面基本信息
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title
                self.logger.info(f"📄 页面信息 - 标题: {page_title}")
                self.logger.info(f"📄 实际URL: {current_url}")

                if current_url != url:
                    self.logger.warning(f"⚠️ URL重定向: {url} -> {current_url}")
            except Exception as e:
                self.logger.error(f"❌ 获取页面基本信息失败: {e}")

            # 快速等待页面加载
            self.logger.info("⏳ 等待页面准备就绪...")
            ready_start = time.time()
            page_ready = self._wait_for_page_ready()
            ready_time = time.time() - ready_start
            self.logger.info(f"📊 页面准备状态: {page_ready} (耗时: {ready_time:.2f}秒)")

            if not page_ready:
                self.logger.warning(f"⚠️ 页面加载超时: {url}")
                # 即使超时也尝试提取链接
                self.logger.info("🔍 尝试在超时状态下提取链接...")
                try:
                    artwork_links = self._extract_artwork_links_optimized()
                    if artwork_links:
                        self.logger.info(f"✅ 超时状态下仍获取到 {len(artwork_links)} 个链接")
                        return artwork_links
                except Exception as e:
                    self.logger.error(f"❌ 超时状态下提取链接失败: {e}")
                return set()

            # 根据模式选择滚动策略
            self.logger.info(f"📜 开始滚动页面 (快速模式: {fast_mode})")
            scroll_start = time.time()

            if fast_mode:
                self._smart_scroll_optimized()
            else:
                self._smart_scroll()

            scroll_time = time.time() - scroll_start
            self.logger.info(f"✅ 页面滚动完成 (耗时: {scroll_time:.2f}秒)")

            # 获取所有作品链接
            self.logger.info("🔍 开始最终链接提取...")
            extract_start = time.time()
            artwork_links = self._extract_artwork_links_optimized()
            extract_time = time.time() - extract_start
            self.logger.info(f"✅ 链接提取完成，共 {len(artwork_links)} 个 (耗时: {extract_time:.2f}秒)")

            # 检查是否可能是登录或内容问题
            if len(artwork_links) <= 1 and "bookmark_new_illust" in url:
                self.logger.info("🔍 检查登录和内容状态...")
                login_required = self._check_login_and_content_status()
                if login_required:
                    self.logger.error("❌ 检测到需要登录，请在浏览器中完成登录后重试")

            # 缓存结果
            if artwork_links:
                self._cache_page_links(url, artwork_links)
                self.logger.info("💾 结果已缓存")
            else:
                self.logger.warning("⚠️ 没有链接可缓存")

            total_time = time.time() - start_time
            self.logger.info(f"🏁 页面处理完成: {url}")
            self.logger.info(f"📊 总耗时: {total_time:.2f}秒, 获取到 {len(artwork_links)} 个作品链接")

            return artwork_links

        except Exception as e:
            total_time = time.time() - start_time
            self.logger.error(f"❌ 获取页面链接失败: {url}, 错误: {e}")
            self.logger.error(f"⏱️ 失败前耗时: {total_time:.2f}秒")
            import traceback
            self.logger.error(f"💥 错误详情: {traceback.format_exc()}")

            # 尝试获取更多调试信息
            try:
                current_url = self.driver.current_url
                page_source_length = len(self.driver.page_source)
                self.logger.error(f"🌐 错误时状态 - URL: {current_url}, 页面长度: {page_source_length}")
            except:
                self.logger.error("❌ 无法获取错误时的页面状态")

            return set()

    def _cache_page_links(self, url: str, links: Set[str]) -> None:
        """
        缓存页面链接

        Args:
            url: 页面URL
            links: 作品链接集合
        """
        # 如果缓存已满，移除最旧的条目
        if len(self._page_cache) >= self._cache_max_size:
            # 移除第一个（最旧的）条目
            oldest_url = next(iter(self._page_cache))
            del self._page_cache[oldest_url]

        self._page_cache[url] = links.copy()

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计
        """
        return {
            'cache_size': len(self._page_cache),
            'cache_max_size': self._cache_max_size,
            'cache_hit_rate': self._calculate_cache_hit_rate(),
            'driver_status': 'active' if self.driver else 'inactive'
        }

    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率（简化版本）"""
        # 这里可以添加更复杂的统计逻辑
        return len(self._page_cache) / max(self._cache_max_size, 1) * 100

    def clear_cache(self) -> None:
        """清理页面缓存"""
        cache_size = len(self._page_cache)
        self._page_cache.clear()
        self.logger.info(f"页面缓存已清理，释放了 {cache_size} 个缓存项")
    
    def _smart_scroll(self) -> None:
        """智能滚动策略（保持向后兼容）"""
        self._smart_scroll_optimized()

    def _smart_scroll_optimized(self) -> None:
        """稳定的智能滚动策略 - 超详细调试版本"""
        self.logger.info("🚀 开始智能滚动...")

        try:
            # 检查当前页面内容
            self.logger.info("📊 检查页面初始状态...")
            initial_links = len(self._extract_artwork_links_simple())
            self.logger.info(f"📊 页面初始链接数: {initial_links}")

            # 获取页面尺寸信息
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            viewport_height = self.driver.execute_script("return window.innerHeight")
            self.logger.info(f"📏 页面初始高度: {last_height}px")
            self.logger.info(f"📏 视窗高度: {viewport_height}px")

            scroll_attempts = 0
            max_scrolls = 5  # 减少滚动次数但保持稳定
            no_new_content_count = 0

            while scroll_attempts < max_scrolls and no_new_content_count < 2:
                self.logger.info(f"🔄 开始第 {scroll_attempts + 1}/{max_scrolls} 次滚动...")

                # 渐进式滚动
                current_position = self.driver.execute_script("return window.pageYOffset")
                scroll_step = 1000  # 适中的滚动步长
                new_position = current_position + scroll_step

                self.logger.info(f"📍 当前位置: {current_position}px, 目标位置: {new_position}px")

                # 执行滚动
                self.logger.info("⬇️ 执行滚动操作...")
                self.driver.execute_script(f"window.scrollTo(0, {new_position});")

                # 验证滚动是否成功
                actual_position = self.driver.execute_script("return window.pageYOffset")
                self.logger.info(f"✅ 滚动后实际位置: {actual_position}px")

                # 适中的等待时间
                self.logger.info("⏳ 等待页面内容加载...")
                time.sleep(1.2)

                # 检查是否有新内容加载
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                self.logger.info(f"📏 滚动后页面高度: {new_height}px (变化: {new_height - last_height}px)")

                # 检查页面是否还在加载
                ready_state = self.driver.execute_script("return document.readyState")
                self.logger.info(f"📄 页面状态: {ready_state}")

                self.logger.info("🔍 重新提取作品链接...")
                current_links = len(self._extract_artwork_links_simple())
                self.logger.info(f"📊 当前链接数: {current_links} (变化: {current_links - initial_links})")

                # 如果高度没有变化且链接数量没有增加
                if new_height == last_height and current_links <= initial_links:
                    no_new_content_count += 1
                    self.logger.warning(f"⚠️ 没有新内容，连续计数: {no_new_content_count}/2")
                else:
                    no_new_content_count = 0
                    initial_links = current_links
                    self.logger.info("✅ 发现新内容，重置计数")

                last_height = new_height
                scroll_attempts += 1

                # 如果已经滚动到底部，提前退出
                if new_position >= new_height:
                    self.logger.info("🏁 已滚动到页面底部，提前退出")
                    break

                # 检查是否有JavaScript错误
                try:
                    js_errors = self.driver.execute_script("return window.jsErrors || []")
                    if js_errors:
                        self.logger.warning(f"⚠️ 检测到JavaScript错误: {js_errors}")
                except:
                    pass

            self.logger.info(f"🏁 滚动完成！总计滚动 {scroll_attempts} 次，最终链接数: {initial_links}")

            # 最终状态检查
            final_position = self.driver.execute_script("return window.pageYOffset")
            final_height = self.driver.execute_script("return document.body.scrollHeight")
            self.logger.info(f"📊 最终状态 - 位置: {final_position}px, 高度: {final_height}px")

        except Exception as e:
            self.logger.error(f"❌ 智能滚动失败: {e}")
            import traceback
            self.logger.error(f"💥 滚动错误详情: {traceback.format_exc()}")

            # 尝试获取更多错误信息
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title
                self.logger.error(f"🌐 错误时页面信息 - URL: {current_url}, 标题: {page_title}")
            except:
                pass
    
    def wait_for_element(self, selector: str, timeout: int = None) -> bool:
        """
        等待元素出现
        
        Args:
            selector: CSS选择器
            timeout: 超时时间
            
        Returns:
            bool: 是否找到元素
        """
        if timeout is None:
            timeout = self.spider_config.selenium_timeout
        
        try:
            wait = WebDriverWait(self.driver, timeout)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
            return True
        except TimeoutException:
            return False
        except Exception as e:
            self.logger.error(f"等待元素失败: {e}")
            return False
    
    def _find_elements_by_selectors(self, selector_group: str) -> bool:
        """
        使用选择器组查找元素

        Args:
            selector_group: 选择器组名称

        Returns:
            bool: 是否找到元素
        """
        selectors = self.SELECTORS.get(selector_group, [])
        for selector in selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    return True
            except Exception:
                continue
        return False

    def _extract_artwork_links(self) -> Set[str]:
        """
        从当前页面提取作品链接（保持向后兼容）

        Returns:
            Set[str]: 作品链接集合
        """
        return self._extract_artwork_links_optimized()

    def _extract_artwork_links_simple(self) -> Set[str]:
        """
        简化的作品链接提取（用于滚动过程中的检查，无详细日志）

        Returns:
            Set[str]: 作品链接集合
        """
        artwork_links = set()

        # 按优先级排序的选择器
        prioritized_selectors = [
            'a[href*="/artworks/"]',  # 最常见的作品链接
            'a[href^="/artworks/"]',  # 相对路径的作品链接
            '.gtm-new-work-thumbnail-click',  # 特定的作品缩略图
            '[data-gtm-value*="artworks"]'  # 包含artworks的数据属性
        ]

        try:
            # 组合选择器，一次查询
            combined_selector = ', '.join(prioritized_selectors)
            elements = self.driver.find_elements(By.CSS_SELECTOR, combined_selector)

            for element in elements:
                try:
                    href = element.get_attribute('href')
                    if href and '/artworks/' in href:
                        # 标准化URL
                        if not href.startswith('https://'):
                            href = 'https://www.pixiv.net' + href
                        artwork_links.add(href)
                except Exception:
                    continue

        except Exception:
            # 如果组合选择器失败，回退到逐个尝试
            for selector in prioritized_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        try:
                            href = element.get_attribute('href')
                            if href and '/artworks/' in href:
                                # 标准化URL
                                if not href.startswith('https://'):
                                    href = 'https://www.pixiv.net' + href
                                artwork_links.add(href)
                        except Exception:
                            continue
                except Exception:
                    continue

        # 验证和清理链接
        validated_links = self._validate_artwork_links(artwork_links)
        return validated_links

    def _extract_artwork_links_optimized(self) -> Set[str]:
        """
        优化的作品链接提取 - 超详细调试版本

        Returns:
            Set[str]: 作品链接集合
        """
        self.logger.info("🔍 开始提取作品链接...")
        artwork_links = set()

        # 首先检查页面基本状态
        try:
            page_title = self.driver.title
            current_url = self.driver.current_url
            page_source_length = len(self.driver.page_source)
            self.logger.info(f"📄 页面状态 - 标题: {page_title}")
            self.logger.info(f"📄 当前URL: {current_url}")
            self.logger.info(f"📄 页面源码长度: {page_source_length} 字符")
        except Exception as e:
            self.logger.error(f"❌ 获取页面基本信息失败: {e}")

        # 按优先级排序的选择器
        prioritized_selectors = [
            'a[href*="/artworks/"]',  # 最常见的作品链接
            'a[href^="/artworks/"]',  # 相对路径的作品链接
            '.gtm-new-work-thumbnail-click',  # 特定的作品缩略图
            '[data-gtm-value*="artworks"]'  # 包含artworks的数据属性
        ]

        # 尝试使用单个CSS选择器一次性获取所有链接
        try:
            # 组合选择器，一次查询
            combined_selector = ', '.join(prioritized_selectors)
            self.logger.info(f"🔍 使用组合选择器: {combined_selector}")

            self.logger.info("⏳ 正在查找元素...")
            elements = self.driver.find_elements(By.CSS_SELECTOR, combined_selector)
            self.logger.info(f"✅ 找到 {len(elements)} 个匹配元素")

            if len(elements) == 0:
                self.logger.warning("⚠️ 没有找到任何匹配的元素，可能页面结构有变化")
                # 尝试查找所有链接元素
                all_links = self.driver.find_elements(By.TAG_NAME, 'a')
                self.logger.info(f"📊 页面总共有 {len(all_links)} 个链接元素")

                # 检查前10个链接的href属性
                for i, link in enumerate(all_links[:10]):
                    try:
                        href = link.get_attribute('href')
                        self.logger.info(f"🔗 链接 {i+1}: {href}")
                    except:
                        self.logger.info(f"🔗 链接 {i+1}: 无法获取href")

            for i, element in enumerate(elements):
                try:
                    href = element.get_attribute('href')
                    tag_name = element.tag_name
                    class_name = element.get_attribute('class')

                    self.logger.info(f"🔍 元素 {i+1}: 标签={tag_name}, 类名={class_name}, href={href}")

                    if href and '/artworks/' in href:
                        # 标准化URL
                        if not href.startswith('https://'):
                            href = 'https://www.pixiv.net' + href
                        artwork_links.add(href)
                        self.logger.info(f"✅ 有效作品链接 {len(artwork_links)}: {href}")
                    else:
                        self.logger.info(f"❌ 无效链接: {href}")

                except Exception as e:
                    self.logger.error(f"❌ 处理元素 {i+1} 失败: {e}")
                    continue

        except Exception as e:
            # 如果组合选择器失败，回退到逐个尝试
            self.logger.error(f"❌ 组合选择器失败，回退到逐个尝试: {e}")

            for selector_idx, selector in enumerate(prioritized_selectors):
                try:
                    self.logger.info(f"🔍 尝试选择器 {selector_idx+1}/{len(prioritized_selectors)}: {selector}")
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    self.logger.info(f"📊 选择器 {selector} 找到 {len(elements)} 个元素")

                    for element in elements:
                        try:
                            href = element.get_attribute('href')
                            if href and '/artworks/' in href:
                                # 标准化URL
                                if not href.startswith('https://'):
                                    href = 'https://www.pixiv.net' + href
                                artwork_links.add(href)
                                self.logger.info(f"✅ 通过选择器 {selector} 找到作品链接: {href}")
                        except Exception:
                            continue
                except Exception as e:
                    self.logger.error(f"❌ 选择器 {selector} 失败: {e}")
                    continue

        self.logger.info(f"📊 原始链接提取完成，共 {len(artwork_links)} 个链接")

        # 验证和清理链接
        self.logger.info("🔍 开始验证链接...")
        validated_links = self._validate_artwork_links(artwork_links)
        self.logger.info(f"✅ 链接验证完成，有效链接 {len(validated_links)} 个")

        if len(validated_links) > 0:
            self.logger.info("📋 有效链接列表:")
            for i, link in enumerate(list(validated_links)[:5]):  # 只显示前5个
                self.logger.info(f"  {i+1}. {link}")
        else:
            self.logger.warning("⚠️ 没有找到任何有效的作品链接")

        return validated_links

    def _validate_artwork_links(self, links: Set[str]) -> Set[str]:
        """
        验证和清理作品链接

        Args:
            links: 原始链接集合

        Returns:
            Set[str]: 验证后的链接集合
        """
        validated_links = set()

        for link in links:
            try:
                # 提取作品ID进行验证
                import re
                match = re.search(r'/artworks/(\d+)', link)
                if match:
                    artwork_id = match.group(1)
                    # 确保ID是有效的数字
                    if artwork_id.isdigit() and len(artwork_id) <= 12:  # Pixiv作品ID通常不超过12位
                        # 标准化URL格式
                        standard_url = f"https://www.pixiv.net/artworks/{artwork_id}"
                        validated_links.add(standard_url)
            except Exception:
                continue

        if len(validated_links) != len(links):
            self.logger.debug(f"链接验证: {len(links)} -> {len(validated_links)}")

        return validated_links

    def _check_login_and_content_status(self):
        """检查登录状态和内容状态，返回是否需要登录"""
        try:
            # 检查是否有登录相关的元素
            login_indicators = [
                'a[href*="/login"]',  # 登录链接
                '.login-form',        # 登录表单
                '[data-gtm-value="login"]'  # 登录按钮
            ]

            has_login_elements = False
            for selector in login_indicators:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        has_login_elements = True
                        break
                except:
                    continue

            # 检查页面内容
            page_text = self.driver.find_element(By.TAG_NAME, "body").text.lower()

            if has_login_elements or "login" in page_text or "ログイン" in page_text:
                self.logger.warning("⚠️ 检测到登录页面或登录提示，用户可能未登录")
                self.logger.warning("请先登录Pixiv账户后再开始下载")
                return True  # 需要登录
            elif "フォロー" in page_text or "follow" in page_text:
                if "作品がありません" in page_text or "no works" in page_text:
                    self.logger.warning("⚠️ 关注的画师暂时没有新作品")
                    self.logger.warning("建议关注更多画师或稍后再试")
                else:
                    self.logger.warning("⚠️ 关注画师新作品页面内容较少")
                    self.logger.warning("可能需要关注更多活跃的画师")
                return False  # 已登录但内容少
            else:
                self.logger.warning("⚠️ 页面内容异常，可能存在网络问题或页面结构变化")
                return False  # 状态不明确

        except Exception as e:
            self.logger.debug(f"检查登录状态失败: {e}")
            return False

    def check_login_status(self) -> bool:
        """
        检查登录状态

        Returns:
            bool: 是否已登录
        """
        try:
            self.driver.get('https://www.pixiv.net/')
            time.sleep(3)

            # 检查登录相关元素
            if self._find_elements_by_selectors('user_menu'):
                self.logger.info("检测到登录状态")
                return True

            # 检查是否有登录按钮（说明未登录）
            if self._find_elements_by_selectors('login_buttons'):
                self.logger.warning("检测到登录按钮，可能未登录")
                return False
            
            # 检查URL
            current_url = self.driver.current_url
            if 'login' in current_url or 'signup' in current_url:
                self.logger.warning("当前在登录页面")
                return False
            
            self.logger.info("无法确定登录状态，假设已登录")
            return True
            
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False
    
    def get_search_results(self, keyword: str, pages: int = 5, search_config=None) -> Set[str]:
        """
        搜索作品并获取结果链接

        Args:
            keyword: 搜索关键词
            pages: 搜索页数
            search_config: 搜索配置对象（可选）

        Returns:
            Set[str]: 作品链接集合
        """
        all_links = set()

        try:
            for page in range(1, pages + 1):
                if search_config:
                    # 使用搜索配置生成URL
                    search_url = search_config.get_search_url(keyword, page)
                else:
                    # 回退到默认URL格式
                    search_url = f"https://www.pixiv.net/tags/{keyword}/artworks?p={page}"

                links = self.get_single_page_links(search_url)
                all_links.update(links)

                # 添加延迟
                time.sleep(2)

            self.logger.info(f"搜索 '{keyword}' 完成，共找到 {len(all_links)} 个作品")
            return all_links

        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            return set()
    
    def get_user_artworks(self, user_id: int, pages: int = 5) -> Set[str]:
        """
        获取用户作品链接
        
        Args:
            user_id: 用户ID
            pages: 页数
            
        Returns:
            Set[str]: 作品链接集合
        """
        all_links = set()
        
        try:
            for page in range(1, pages + 1):
                user_url = f"https://www.pixiv.net/users/{user_id}/artworks?p={page}"
                links = self.get_single_page_links(user_url)
                all_links.update(links)
                
                # 如果没有找到新链接，说明没有更多页面
                if not links:
                    break
                
                time.sleep(2)
            
            self.logger.info(f"用户 {user_id} 作品获取完成，共 {len(all_links)} 个作品")
            return all_links
            
        except Exception as e:
            self.logger.error(f"获取用户作品失败: {e}")
            return set()
    
    def quit(self) -> None:
        """关闭驱动器并清理所有资源"""
        if self.driver:
            try:
                self.logger.info("🧹 开始清理Selenium驱动器...")

                # 清理页面缓存
                if hasattr(self, '_page_cache'):
                    cache_size = len(self._page_cache)
                    self._page_cache.clear()
                    self.logger.info(f"📦 页面缓存已清理，释放了 {cache_size} 个缓存项")

                # 先关闭所有窗口
                try:
                    window_handles = self.driver.window_handles
                    self.logger.info(f"🪟 正在关闭 {len(window_handles)} 个浏览器窗口...")
                    for handle in window_handles:
                        try:
                            self.driver.switch_to.window(handle)
                            self.driver.close()
                        except Exception as e:
                            self.logger.debug(f"关闭窗口 {handle} 失败: {e}")
                except Exception as e:
                    self.logger.debug(f"获取窗口句柄失败: {e}")

                # 清理浏览器数据
                try:
                    self.driver.delete_all_cookies()
                    self.logger.info("🍪 浏览器Cookie已清理")
                except Exception as e:
                    self.logger.debug(f"清理Cookie失败: {e}")

                # 退出驱动器
                self.driver.quit()
                self.logger.info("✅ Selenium驱动器已完全关闭")

            except Exception as e:
                self.logger.error(f"❌ 关闭驱动器失败: {e}")

            # 精确清理爬虫控制的进程
            self._cleanup_spider_processes()

            finally:
                self.driver = None
                self._driver_pid = None
                self._browser_pid = None
    
    def __enter__(self):
        """支持上下文管理器"""
        return self
    
    def _cleanup_spider_processes(self) -> None:
        """精确清理爬虫控制的进程，不影响用户的浏览器"""
        try:
            import psutil

            # 清理记录的ChromeDriver进程
            if self._driver_pid:
                try:
                    driver_proc = psutil.Process(self._driver_pid)
                    if driver_proc.is_running() and 'chromedriver' in driver_proc.name().lower():
                        driver_proc.terminate()
                        self.logger.info(f"🔪 已终止爬虫的ChromeDriver进程: {self._driver_pid}")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    self.logger.debug(f"ChromeDriver进程 {self._driver_pid} 已不存在或无权限: {e}")
                except Exception as e:
                    self.logger.debug(f"终止ChromeDriver进程失败: {e}")

            # 清理记录的Chrome浏览器进程
            if self._browser_pid:
                try:
                    browser_proc = psutil.Process(self._browser_pid)
                    if browser_proc.is_running() and 'chrome' in browser_proc.name().lower():
                        browser_proc.terminate()
                        self.logger.info(f"🔪 已终止爬虫的Chrome浏览器进程: {self._browser_pid}")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    self.logger.debug(f"Chrome进程 {self._browser_pid} 已不存在或无权限: {e}")
                except Exception as e:
                    self.logger.debug(f"终止Chrome进程失败: {e}")

        except ImportError:
            self.logger.debug("psutil未安装，无法精确清理进程")
            # 回退到原来的清理方式，但更加谨慎
            self._fallback_cleanup()
        except Exception as e:
            self.logger.debug(f"精确清理进程失败: {e}")
            self._fallback_cleanup()

    def _fallback_cleanup(self) -> None:
        """回退清理方式 - 更加谨慎的进程清理"""
        try:
            import subprocess
            import platform

            if platform.system() == "Windows":
                # 只清理ChromeDriver，不清理Chrome浏览器
                result = subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                                      capture_output=True, check=False)
                if result.returncode == 0:
                    self.logger.info("🔪 已清理ChromeDriver进程（回退方式）")
                    self.logger.warning("⚠️ 使用了全局清理方式，可能影响其他ChromeDriver实例")
            else:
                # Linux/Mac系统只清理ChromeDriver
                subprocess.run(['pkill', '-f', 'chromedriver'],
                             capture_output=True, check=False)
                self.logger.info("🔪 已清理ChromeDriver进程（回退方式）")

        except Exception as e:
            self.logger.debug(f"回退清理失败: {e}")

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持上下文管理器"""
        self.quit()

    def __del__(self):
        """析构函数"""
        self.quit() 