"""
默认设置和常量定义
"""

from pathlib import Path

# 应用信息
APP_NAME = "Pixiv Spider"
APP_VERSION = "3.0.0"
APP_AUTHOR = "Pixiv Spider Team"

# 文件路径
DEFAULT_CONFIG_FILE = "pixiv_config.json"
DEFAULT_COOKIES_FILE = "cookies/pixiv_cookies.pkl"
DEFAULT_CACHE_FILE = "pixiv_cache.pkl"
DEFAULT_LOG_FILE = "pixiv_spider.log"

# 默认目录
DEFAULT_DOWNLOAD_PATH = "pixiv_imgs"
DEFAULT_TEMP_PATH = "temp"
DEFAULT_LOG_PATH = "logs"
DEFAULT_COOKIES_PATH = "cookies"
DEFAULT_COOKIE_PATH = "cookies"  # 新增cookie专用目录

# API相关
PIXIV_BASE_URL = "https://www.pixiv.net"
PIXIV_API_BASE_URL = "https://app-api.pixiv.net"
PIXIV_AJAX_URL = "https://www.pixiv.net/ajax"

# 网络设置
DEFAULT_USER_AGENT = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
    "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
)
DEFAULT_REFERER = "https://www.pixiv.net/"
DEFAULT_TIMEOUT = 30
DEFAULT_RETRY_ATTEMPTS = 3
DEFAULT_RETRY_DELAY = 1.0

# 性能设置
DEFAULT_MAX_WORKERS = 8
DEFAULT_CONCURRENT_DOWNLOADS = 4
DEFAULT_API_CACHE_SIZE = 1000
DEFAULT_CACHE_EXPIRE_TIME = 3600

# Selenium设置
DEFAULT_SELENIUM_TIMEOUT = 10
DEFAULT_PAGE_LOAD_TIMEOUT = 30

# 日志设置
DEFAULT_LOG_LEVEL = "INFO"
DEFAULT_LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
DEFAULT_LOG_BACKUP_COUNT = 5

# GUI设置
DEFAULT_WINDOW_WIDTH = 800
DEFAULT_WINDOW_HEIGHT = 600
DEFAULT_WINDOW_MIN_WIDTH = 600
DEFAULT_WINDOW_MIN_HEIGHT = 400

# 下载设置
DEFAULT_START_PAGE = 1
DEFAULT_END_PAGE = 5
DEFAULT_DAYS = 1
DEFAULT_MIN_BOOKMARKS = 0
DEFAULT_MAX_BOOKMARKS = 0

# 文件扩展名
SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
SUPPORTED_ANIMATION_EXTENSIONS = ['.gif', '.webm', '.mp4']

# 文件大小限制
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
MAX_FILENAME_LENGTH = 255

# 速率限制
REQUEST_INTERVAL = 0.5  # 请求间隔（秒）
MAX_REQUESTS_PER_MINUTE = 60

# 错误重试设置
MAX_DOWNLOAD_RETRIES = 3
DOWNLOAD_RETRY_DELAY = 2.0
MAX_API_RETRIES = 5
API_RETRY_DELAY = 1.0 
