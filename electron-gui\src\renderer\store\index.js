import { createStore } from 'vuex'

// 创建状态管理store
const store = createStore({
  state: {
    // 登录状态
    isLoggedIn: false,
    loginStatus: '检查中...',
    userInfo: null,

    // 下载状态
    isDownloading: false,
    downloadStatus: 'idle',
    downloadProgress: 0,
    downloadStats: {
      total: 0,
      completed: 0,
      failed: 0,
      speed: 0
    },

    // 应用状态
    isConnected: false,
    backendReady: false,

    // 主题设置
    theme: 'auto', // 'light', 'dark', 'auto'

    // 下载设置
    downloadSettings: {
      mode: 'following',
      following: {
        downloadType: 'pages',
        days: 7,
        pageStart: 1,
        pageEnd: 5,
        downloadPath: ''
      },
      search: {
        keyword: '',
        searchType: 'artworks',
        bookmarkCount: 1000,
        searchMode: 'all',
        pageStart: 1,
        pageEnd: 5,
        downloadPath: ''
      },
      ranking: {
        rankingType: 'overall',
        rankingPeriod: 'daily',
        rankingMode: 'safe',
        rankingDate: (() => {
          const today = new Date()
          return today.getFullYear() + '-' +
                 String(today.getMonth() + 1).padStart(2, '0') + '-' +
                 String(today.getDate()).padStart(2, '0')
        })(),
        downloadPath: ''
      },
      artist: {
        artistId: '',
        pageStart: 1,
        pageEnd: 5,
        downloadPath: ''
      },
      // 性能设置
      maxConcurrent: 3,
      delay: 1000,
      retryCount: 3,
      timeout: 30,
      skipExisting: true,
      saveOriginal: true,
      createSubfolder: true
    },

    // 日志
    logs: [],

    // 配置
    config: {}
  },
  
  mutations: {
    // 登录状态相关 - 合并重复的设置方法
    setLoginStatus(state, { isLoggedIn, status, userInfo }) {
      state.isLoggedIn = isLoggedIn
      if (status) state.loginStatus = status
      if (userInfo) state.userInfo = userInfo
    },
    
    // 下载状态相关 - 合并为统一的状态更新方法
    updateDownloadState(state, { status, progress, stats }) {
      if (status !== undefined) state.downloadStatus = status
      if (progress !== undefined) state.downloadProgress = progress
      if (stats !== undefined) state.downloadStats = { ...state.downloadStats, ...stats }
    },
    
    // 连接状态相关
    setConnectionStatus(state, isConnected) {
      state.isConnected = isConnected
    },
    
    setBackendReady(state, ready) {
      state.backendReady = ready
    },
    
    // 配置相关
    setConfig(state, config) {
      state.config = { ...state.config, ...config }
    },

    // 主题相关
    setTheme(state, theme) {
      state.theme = theme
      console.log('🎨 主题已更新为:', theme)
    },

    // 下载设置相关
    updateDownloadSettings(state, settings) {
      console.log('🔄 更新下载设置:', settings)
      console.log('🔄 当前state.downloadSettings:', state.downloadSettings)

      // 深度合并下载设置
      if (settings.following) {
        state.downloadSettings.following = { ...state.downloadSettings.following, ...settings.following }
        console.log('🔄 更新following设置:', state.downloadSettings.following)
      }
      if (settings.search) {
        state.downloadSettings.search = { ...state.downloadSettings.search, ...settings.search }
        console.log('🔄 更新search设置:', state.downloadSettings.search)
      }
      if (settings.ranking) {
        state.downloadSettings.ranking = { ...state.downloadSettings.ranking, ...settings.ranking }
        console.log('🔄 更新ranking设置:', state.downloadSettings.ranking)
      }
      if (settings.artist) {
        state.downloadSettings.artist = { ...state.downloadSettings.artist, ...settings.artist }
        console.log('🔄 更新artist设置:', state.downloadSettings.artist)
      }

      // 更新其他设置
      Object.keys(settings).forEach(key => {
        if (!['following', 'search', 'ranking', 'artist'].includes(key)) {
          state.downloadSettings[key] = settings[key]
          console.log(`🔄 更新${key}设置:`, settings[key])
        }
      })

      console.log('✅ 更新后的downloadSettings:', state.downloadSettings)
    },

    setDownloading(state, isDownloading) {
      state.isDownloading = isDownloading
    },

    // 日志相关
    addLog(state, log) {
      // 确保日志消息是字符串并正确处理中文
      const message = typeof log === 'string' ? log : String(log)

      state.logs.push({
        id: Date.now() + Math.random(), // 确保唯一性
        timestamp: new Date().toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        message: message.trim(),
        type: 'info'
      })

      // 限制日志数量，保持性能
      if (state.logs.length > 1000) {
        state.logs = state.logs.slice(-500)
      }
    },

    clearLogs(state) {
      state.logs = []
    }
  },
  
  actions: {
    // 初始化应用状态
    async initializeApp({ commit, dispatch }) {
      try {
        // 检查后端连接
        commit('setConnectionStatus', true)
        commit('setBackendReady', true)
        
        // 检查登录状态
        await dispatch('checkLoginStatus')
        
        // 加载配置
        await dispatch('loadConfig')
        
      } catch (error) {
        console.error('初始化应用失败:', error)
        commit('setConnectionStatus', false)
      }
    },
    
    // 检查登录状态
    async checkLoginStatus({ commit }) {
      try {
        const api = window.electronAPI
        if (!api) throw new Error('API not available')
        
        const response = await api.getAuthStatus()
        
        commit('setLoginStatus', {
          isLoggedIn: response.authenticated || false,
          status: response.authenticated ? '已登录' : '未登录',
          userInfo: response.user_info
        })
        
        return response
      } catch (error) {
        console.error('检查登录状态失败:', error)
        commit('setLoginStatus', {
          isLoggedIn: false,
          status: '连接失败'
        })
        throw error
      }
    },
    
    // 加载配置
    async loadConfig({ commit }) {
      try {
        const api = window.electronAPI
        if (!api) throw new Error('API not available')
        
        const response = await api.getConfig()
        if (response.success) {
          commit('setConfig', response.config)
        }
        
        return response
      } catch (error) {
        console.error('加载配置失败:', error)
        throw error
      }
    },
    
    // 更新下载状态 - 使用统一的状态更新方法
    updateDownloadStatus({ commit }, statusData) {
      commit('updateDownloadState', statusData)
    },

    // 加载设置
    async loadSettings({ commit }) {
      try {
        console.log('📥 开始加载设置...')

        if (window.electronAPI && window.electronAPI.getStoreValue) {
          console.log('📥 使用Electron API加载设置')
          const settings = await window.electronAPI.getStoreValue('downloadSettings')
          console.log('📥 从Electron Store加载的设置:', settings)

          if (settings) {
            commit('updateDownloadSettings', settings)
            console.log('✅ 下载设置已加载')
          } else {
            console.log('⚠️ 没有找到保存的下载设置')
          }

          // 加载主题设置
          const theme = await window.electronAPI.getStoreValue('theme')
          console.log('📥 从Electron Store加载的主题:', theme)

          if (theme) {
            commit('setTheme', theme)
            console.log('✅ 主题设置已加载')
          } else {
            console.log('⚠️ 没有找到保存的主题设置')
          }
        } else {
          console.log('📥 使用localStorage加载设置')
          // 如果没有Electron API，从localStorage加载
          const settings = localStorage.getItem('downloadSettings')
          console.log('📥 从localStorage加载的设置:', settings)

          if (settings) {
            commit('updateDownloadSettings', JSON.parse(settings))
            console.log('✅ 下载设置已从localStorage加载')
          }

          // 加载主题设置
          const theme = localStorage.getItem('pixiv-spider-theme')
          console.log('📥 从localStorage加载的主题:', theme)

          if (theme) {
            commit('setTheme', theme)
            console.log('✅ 主题设置已从localStorage加载')
          }
        }

        console.log('✅ 设置加载完成')
      } catch (error) {
        console.error('❌ 加载设置失败:', error)
      }
    },

    // 保存设置
    async saveSettings({ state }) {
      try {
        console.log('💾 Store保存设置:', state.downloadSettings)

        // 清理数据，确保所有值都可序列化
        const cleanSettings = JSON.parse(JSON.stringify(state.downloadSettings))
        const cleanTheme = String(state.theme)

        console.log('💾 清理后的设置:', cleanSettings)

        if (window.electronAPI && window.electronAPI.setStoreValue) {
          await window.electronAPI.setStoreValue('downloadSettings', cleanSettings)
          await window.electronAPI.setStoreValue('theme', cleanTheme)
          console.log('✅ 设置已保存到Electron Store')
        } else {
          // 如果没有Electron API，保存到localStorage
          localStorage.setItem('downloadSettings', JSON.stringify(cleanSettings))
          localStorage.setItem('pixiv-spider-theme', cleanTheme)
          console.log('✅ 设置已保存到localStorage')
        }
      } catch (error) {
        console.error('❌ Store保存设置失败:', error)
        console.error('❌ 错误详情:', error.stack)
        throw error
      }
    }
  },
  
  getters: {
    // 精简的getter，移除重复的状态访问器
    isAuthenticated: state => state.isLoggedIn,
    isAppReady: state => state.isConnected && state.backendReady,
    downloadProgressPercent: state => Math.round(state.downloadProgress * 100) / 100,
    downloadStatsText: state => {
      const { total, completed, failed } = state.downloadStats
      return `总计: ${total} | 完成: ${completed} | 失败: ${failed}`
    }
  }
})

export default store
