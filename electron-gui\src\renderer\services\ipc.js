/**
 * IPC通信服务
 * 处理与后端的实时通信（纯IPC实现）
 */

class IPCService {
  constructor() {
    this.listeners = new Map()
    this._isConnected = false

    // 检查electronAPI是否可用
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }
    this.electronAPI = window.electronAPI

    // 设置IPC事件监听
    this._setupEventListeners()
  }

  /**
   * 设置IPC事件监听
   */
  _setupEventListeners() {
    // 后端状态事件
    this.electronAPI.onBackendReady(() => {
      console.log('🔗 Backend connected via IPC')
      this._isConnected = true
      this.emit('connected')
    })

    this.electronAPI.onBackendError((error) => {
      console.error('❌ Backend error:', error)
      this._isConnected = false
      this.emit('error', error)
    })

    // 状态更新事件
    this.electronAPI.onStatusUpdate((data) => {
      this.emit('statusUpdate', data)
    })

    this.electronAPI.onProgressUpdate((data) => {
      this.emit('progressUpdate', data)
    })

    this.electronAPI.onDownloadComplete((data) => {
      this.emit('downloadComplete', data)
    })

    this.electronAPI.onAuthStatus((data) => {
      this.emit('authStatus', data)
    })

    // 日志事件
    this.electronAPI.onLogMessage((data) => {
      this.emit('logMessage', data)
    })
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners(event) {
    if (event) {
      this.listeners.delete(event)
    } else {
      this.listeners.clear()
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('❌ 事件回调执行失败:', error)
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  get isConnected() {
    return this._isConnected
  }

  /**
   * 清理资源
   */
  destroy() {
    this.listeners.clear()
    this._isConnected = false
  }
}

// 创建单例实例
const ipcService = new IPCService()

export default ipcService
