<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    // 加载设置
    this.$store.dispatch('loadSettings')
    
    // 监听Python后端日志
    if (window.electronAPI) {
      // 新的分级日志监听
      window.electronAPI.onPythonInfo((event, data) => {
        this.$store.commit('addLog', data.trim())
      })

      window.electronAPI.onPythonWarning((event, data) => {
        this.$store.commit('addLog', `⚠️ ${data.trim()}`)
      })

      window.electronAPI.onPythonError((event, data) => {
        this.$store.commit('addLog', `❌ ${data.trim()}`)
      })

      // 保留兼容性：监听旧的事件
      window.electronAPI.onPythonLog((event, data) => {
        this.$store.commit('addLog', data.trim())
      })

      // 监听详细日志消息
      window.electronAPI.onLogMessage((event, data) => {
        const logMessage = `[${data.level}] ${data.message}`
        this.$store.commit('addLog', logMessage)
      })
    }
  },
  beforeUnmount() {
    // 清理监听器
    if (window.electronAPI) {
      window.electronAPI.removeAllListeners('python-log')
      window.electronAPI.removeAllListeners('python-error')
      window.electronAPI.removeAllListeners('python-info')
      window.electronAPI.removeAllListeners('python-warning')
      window.electronAPI.removeAllListeners('log-message')
    }
  }
}
</script>

<style>
#app {
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}
</style>
