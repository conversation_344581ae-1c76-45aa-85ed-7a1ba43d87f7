/**
 * 统一错误处理服务
 * 解决前后端重复的错误处理逻辑
 */

class ErrorHandlerService {
  constructor() {
    this.errorTypes = {
      // 网络错误
      NETWORK_ERROR: 'NETWORK_ERROR',
      CONNECTION_ERROR: 'CONNECTION_ERROR',
      TIMEOUT_ERROR: 'TIMEOUT_ERROR',
      
      // 认证错误
      AUTH_ERROR: 'AUTH_ERROR',
      LOGIN_REQUIRED: 'LOGIN_REQUIRED',
      TOKEN_EXPIRED: 'TOKEN_EXPIRED',
      
      // 配置错误
      CONFIG_ERROR: 'CONFIG_ERROR',
      VALIDATION_ERROR: 'VALIDATION_ERROR',
      
      // 下载错误
      DOWNLOAD_ERROR: 'DOWNLOAD_ERROR',
      FILE_ERROR: 'FILE_ERROR',
      
      // 系统错误
      SYSTEM_ERROR: 'SYSTEM_ERROR',
      UNKNOWN_ERROR: 'UNKNOWN_ERROR'
    }

    this.errorMessages = {
      [this.errorTypes.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
      [this.errorTypes.CONNECTION_ERROR]: '无法连接到服务器，请稍后重试',
      [this.errorTypes.TIMEOUT_ERROR]: '请求超时，请检查网络连接',
      
      [this.errorTypes.AUTH_ERROR]: '认证失败，请重新登录',
      [this.errorTypes.LOGIN_REQUIRED]: '请先登录Pixiv账户',
      [this.errorTypes.TOKEN_EXPIRED]: '登录已过期，请重新登录',
      
      [this.errorTypes.CONFIG_ERROR]: '配置错误，请检查设置',
      [this.errorTypes.VALIDATION_ERROR]: '输入验证失败，请检查输入内容',
      
      [this.errorTypes.DOWNLOAD_ERROR]: '下载失败，请重试',
      [this.errorTypes.FILE_ERROR]: '文件操作失败，请检查文件权限',
      
      [this.errorTypes.SYSTEM_ERROR]: '系统错误，请联系技术支持',
      [this.errorTypes.UNKNOWN_ERROR]: '未知错误，请重试'
    }
  }

  /**
   * 处理API错误 - 简化版，主要依赖后端统一错误处理
   */
  handleApiError(error, context = '') {
    // 如果是后端返回的标准化错误，直接使用
    if (error.error_type && error.message) {
      this._logError({ type: error.error_type, userMessage: error.message }, context)
      return {
        type: error.error_type,
        message: error.message,
        originalError: error,
        context
      }
    }

    // 否则进行简单的错误解析
    const errorInfo = this._parseError(error)
    this._logError(errorInfo, context)

    return {
      type: errorInfo.type,
      message: errorInfo.userMessage,
      originalError: error,
      context
    }
  }

  /**
   * 处理通用错误 - 合并网络错误和认证错误处理
   */
  handleError(error, context = '') {
    // 优先使用后端的标准化错误格式
    if (error.error_type && error.message) {
      return this.handleApiError(error, context)
    }

    // 简化的错误类型判断
    let errorType = this.errorTypes.UNKNOWN_ERROR
    const errorMsg = (error.message || error.toString()).toLowerCase()

    if (errorMsg.includes('network') || errorMsg.includes('connection')) {
      errorType = this.errorTypes.NETWORK_ERROR
    } else if (errorMsg.includes('timeout')) {
      errorType = this.errorTypes.TIMEOUT_ERROR
    } else if (errorMsg.includes('auth') || errorMsg.includes('login')) {
      errorType = this.errorTypes.AUTH_ERROR
    }

    return {
      type: errorType,
      message: this.errorMessages[errorType],
      originalError: error,
      context
    }
  }

  /**
   * 处理配置错误
   */
  handleConfigError(error, validationErrors = []) {
    return {
      type: this.errorTypes.CONFIG_ERROR,
      message: validationErrors.length > 0 
        ? validationErrors.join('; ') 
        : this.errorMessages[this.errorTypes.CONFIG_ERROR],
      validationErrors,
      originalError: error
    }
  }

  /**
   * 处理下载错误
   */
  handleDownloadError(error) {
    let errorType = this.errorTypes.DOWNLOAD_ERROR
    
    if (error.message?.includes('file') || error.message?.includes('permission')) {
      errorType = this.errorTypes.FILE_ERROR
    }
    
    return {
      type: errorType,
      message: this.errorMessages[errorType],
      originalError: error
    }
  }

  /**
   * 解析错误信息
   */
  _parseError(error) {
    // 如果是字符串错误
    if (typeof error === 'string') {
      return {
        type: this._detectErrorType(error),
        userMessage: error,
        originalMessage: error
      }
    }

    // 如果是Error对象
    if (error instanceof Error) {
      const errorType = this._detectErrorType(error.message)
      return {
        type: errorType,
        userMessage: this.errorMessages[errorType] || error.message,
        originalMessage: error.message
      }
    }

    // 如果是API响应错误
    if (error.response) {
      const status = error.response.status
      let errorType = this.errorTypes.UNKNOWN_ERROR
      
      if (status === 401 || status === 403) {
        errorType = this.errorTypes.AUTH_ERROR
      } else if (status >= 500) {
        errorType = this.errorTypes.SYSTEM_ERROR
      } else if (status >= 400) {
        errorType = this.errorTypes.CONFIG_ERROR
      }
      
      return {
        type: errorType,
        userMessage: this.errorMessages[errorType],
        originalMessage: error.response.data?.message || error.message
      }
    }

    // 默认处理
    return {
      type: this.errorTypes.UNKNOWN_ERROR,
      userMessage: this.errorMessages[this.errorTypes.UNKNOWN_ERROR],
      originalMessage: error.toString()
    }
  }

  /**
   * 检测错误类型
   */
  _detectErrorType(message) {
    const msg = message.toLowerCase()
    
    if (msg.includes('auth') || msg.includes('login') || msg.includes('认证')) {
      return this.errorTypes.AUTH_ERROR
    }
    
    if (msg.includes('network') || msg.includes('connection') || msg.includes('网络')) {
      return this.errorTypes.NETWORK_ERROR
    }
    
    if (msg.includes('timeout') || msg.includes('超时')) {
      return this.errorTypes.TIMEOUT_ERROR
    }
    
    if (msg.includes('config') || msg.includes('validation') || msg.includes('配置')) {
      return this.errorTypes.CONFIG_ERROR
    }
    
    if (msg.includes('download') || msg.includes('file') || msg.includes('下载')) {
      return this.errorTypes.DOWNLOAD_ERROR
    }
    
    return this.errorTypes.UNKNOWN_ERROR
  }

  /**
   * 记录错误日志
   */
  _logError(errorInfo, context) {
    const logMessage = `[${errorInfo.type}] ${context ? `${context}: ` : ''}${errorInfo.originalMessage}`
    console.error(logMessage)
    
    // 可以在这里添加更多的日志记录逻辑
    // 比如发送到日志服务、本地存储等
  }

  /**
   * 创建用户友好的错误消息
   */
  createUserMessage(error, suggestions = []) {
    const errorInfo = this._parseError(error)
    let message = errorInfo.userMessage
    
    if (suggestions.length > 0) {
      message += '\n\n建议：\n' + suggestions.map(s => `• ${s}`).join('\n')
    }
    
    return message
  }

  /**
   * 检查是否为特定类型的错误
   */
  isErrorType(error, type) {
    const errorInfo = this._parseError(error)
    return errorInfo.type === type
  }

  /**
   * 获取错误的重试建议
   */
  getRetryAdvice(error) {
    const errorInfo = this._parseError(error)
    
    switch (errorInfo.type) {
      case this.errorTypes.NETWORK_ERROR:
      case this.errorTypes.CONNECTION_ERROR:
        return ['检查网络连接', '稍后重试', '检查防火墙设置']
        
      case this.errorTypes.TIMEOUT_ERROR:
        return ['检查网络速度', '稍后重试', '减少并发请求数']
        
      case this.errorTypes.AUTH_ERROR:
        return ['重新登录', '检查账户状态', '清除浏览器缓存']
        
      case this.errorTypes.CONFIG_ERROR:
        return ['检查配置设置', '重置为默认配置', '联系技术支持']
        
      case this.errorTypes.DOWNLOAD_ERROR:
        return ['检查磁盘空间', '检查文件权限', '更换下载路径']
        
      default:
        return ['重试操作', '重启应用', '联系技术支持']
    }
  }
}

// 创建单例实例
const errorHandlerService = new ErrorHandlerService()

export default errorHandlerService
