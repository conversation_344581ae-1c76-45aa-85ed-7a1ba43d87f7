// 主题管理混入 - 支持真正的深色/浅色模式切换
export const themeMixin = {
  data() {
    return {
      themeWatcher: null
    }
  },

  computed: {
    currentTheme() {
      return this.$store.state.theme || 'auto'
    },

    isDarkMode() {
      if (this.currentTheme === 'auto') {
        return this.getSystemTheme() === 'dark'
      }
      return this.currentTheme === 'dark'
    },

    isLightMode() {
      return !this.isDarkMode
    },

    themeClass() {
      return {
        'dark-theme': this.isDarkMode,
        'light-theme': this.isLightMode
      }
    }
  },

  mounted() {
    this.setupThemeWatcher()
  },

  beforeUnmount() {
    if (this.themeWatcher) {
      this.themeWatcher.removeEventListener('change', this.handleSystemThemeChange)
    }
  },

  methods: {
    getSystemTheme() {
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark'
      }
      return 'light'
    },

    setupThemeWatcher() {
      if (window.matchMedia) {
        this.themeWatcher = window.matchMedia('(prefers-color-scheme: dark)')
        this.themeWatcher.addEventListener('change', this.handleSystemThemeChange)
      }
    },

    handleSystemThemeChange() {
      if (this.currentTheme === 'auto') {
        this.$nextTick(() => {
          this.applyTheme()
        })
      }
    },

    applyTheme() {
      let themeToApply = 'light'

      if (this.currentTheme === 'dark') {
        themeToApply = 'dark'
      } else if (this.currentTheme === 'light') {
        themeToApply = 'light'
      } else if (this.currentTheme === 'auto') {
        themeToApply = this.getSystemTheme()
      }

      // 应用主题到DOM元素
      document.documentElement.setAttribute('data-theme', themeToApply)
      document.body.setAttribute('data-theme', themeToApply)

      // 移除所有主题类名
      document.body.classList.remove('dark-theme', 'light-theme')
      document.documentElement.classList.remove('dark-theme', 'light-theme')

      // 添加对应的主题类名
      if (themeToApply === 'dark') {
        document.body.classList.add('dark-theme')
        document.documentElement.classList.add('dark-theme')
      } else {
        document.body.classList.add('light-theme')
        document.documentElement.classList.add('light-theme')
      }
    },

    setTheme(theme) {
      this.$store.commit('setTheme', theme)
      this.applyTheme()
      this.saveThemeToStorage(theme)
    },

    saveThemeToStorage(theme) {
      try {
        localStorage.setItem('pixiv-spider-theme', theme)
      } catch (error) {
        console.warn('无法保存主题设置到本地存储:', error)
      }
    },

    loadThemeFromStorage() {
      try {
        const savedTheme = localStorage.getItem('pixiv-spider-theme')
        if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
          this.$store.commit('setTheme', savedTheme)
          return savedTheme
        }
      } catch (error) {
        console.warn('无法从本地存储加载主题设置:', error)
      }
      return 'auto'
    }
  }
}
