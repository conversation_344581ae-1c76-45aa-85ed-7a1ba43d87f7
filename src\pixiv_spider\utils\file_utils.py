"""
文件工具类

提供文件操作相关的工具函数
"""

import os
import shutil
import tempfile
import hashlib
from pathlib import Path
from typing import List, Optional, Tuple
import logging


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_directory(path: str) -> bool:
        """
        确保目录存在
        
        Args:
            path: 目录路径
            
        Returns:
            bool: 是否成功
        """
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logging.error(f"创建目录失败: {path}, 错误: {e}")
            return False
    
    @staticmethod
    def safe_filename(filename: str, max_length: int = 255) -> str:
        """
        生成安全的文件名
        
        Args:
            filename: 原始文件名
            max_length: 最大长度
            
        Returns:
            str: 安全的文件名
        """
        # 替换非法字符
        illegal_chars = '<>:"/\\|?*'
        safe_name = filename
        
        for char in illegal_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 去除首尾空格和点
        safe_name = safe_name.strip(' .')
        
        # 限制长度
        if len(safe_name) > max_length:
            name, ext = os.path.splitext(safe_name)
            max_name_length = max_length - len(ext)
            safe_name = name[:max_name_length] + ext
        
        # 如果名字为空，使用默认名称
        if not safe_name:
            safe_name = "unnamed"
        
        return safe_name
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 文件大小（字节），失败返回-1
        """
        try:
            return os.path.getsize(file_path)
        except (OSError, FileNotFoundError):
            return -1
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5, sha1, sha256)
            
        Returns:
            Optional[str]: 哈希值，失败返回None
        """
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            logging.error(f"计算文件哈希失败: {file_path}, 错误: {e}")
            return None
    
    @staticmethod
    def copy_file_with_progress(src: str, dst: str, progress_callback=None) -> bool:
        """
        带进度的文件复制
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            progress_callback: 进度回调函数(current, total)
            
        Returns:
            bool: 是否成功
        """
        try:
            file_size = FileUtils.get_file_size(src)
            if file_size <= 0:
                return False
            
            FileUtils.ensure_directory(os.path.dirname(dst))
            
            copied = 0
            chunk_size = 64 * 1024  # 64KB
            
            with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
                while True:
                    chunk = fsrc.read(chunk_size)
                    if not chunk:
                        break
                    
                    fdst.write(chunk)
                    copied += len(chunk)
                    
                    if progress_callback:
                        progress_callback(copied, file_size)
            
            return True
            
        except Exception as e:
            logging.error(f"文件复制失败: {src} -> {dst}, 错误: {e}")
            return False
    
    @staticmethod
    def move_file(src: str, dst: str) -> bool:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            FileUtils.ensure_directory(os.path.dirname(dst))
            shutil.move(src, dst)
            return True
        except Exception as e:
            logging.error(f"文件移动失败: {src} -> {dst}, 错误: {e}")
            return False
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception as e:
            logging.error(f"删除文件失败: {file_path}, 错误: {e}")
            return False
    
    @staticmethod
    def clean_empty_directories(root_path: str) -> int:
        """
        清理空目录
        
        Args:
            root_path: 根目录路径
            
        Returns:
            int: 删除的目录数量
        """
        deleted_count = 0
        
        try:
            for root, dirs, files in os.walk(root_path, topdown=False):
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        if not os.listdir(dir_path):  # 目录为空
                            os.rmdir(dir_path)
                            deleted_count += 1
                    except OSError:
                        continue
                        
        except Exception as e:
            logging.error(f"清理空目录失败: {root_path}, 错误: {e}")
        
        return deleted_count
    
    @staticmethod
    def get_temp_file(suffix: str = "", prefix: str = "pixiv_") -> str:
        """
        获取临时文件路径
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            
        Returns:
            str: 临时文件路径
        """
        return tempfile.mktemp(suffix=suffix, prefix=prefix)
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节数
            
        Returns:
            str: 格式化后的大小字符串
        """
        if size_bytes < 0:
            return "未知"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        size = float(size_bytes)
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.2f} {units[unit_index]}"
    
    @staticmethod
    def scan_directory_size(path: str) -> Tuple[int, int]:
        """
        扫描目录大小
        
        Args:
            path: 目录路径
            
        Returns:
            Tuple[int, int]: (总大小字节数, 文件数量)
        """
        total_size = 0
        file_count = 0
        
        try:
            for root, dirs, files in os.walk(path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        size = os.path.getsize(file_path)
                        total_size += size
                        file_count += 1
                    except (OSError, FileNotFoundError):
                        continue
                        
        except Exception as e:
            logging.error(f"扫描目录大小失败: {path}, 错误: {e}")
        
        return total_size, file_count 