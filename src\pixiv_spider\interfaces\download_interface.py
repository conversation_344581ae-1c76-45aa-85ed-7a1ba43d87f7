"""
下载服务接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Callable
from ..models.artwork import Artwork


class IDownloadService(ABC):
    """下载服务接口"""
    
    @abstractmethod
    def batch_download(self, artworks: List[Artwork]) -> Dict[str, int]:
        """
        批量下载作品
        
        Args:
            artworks: 作品列表
            
        Returns:
            Dict[str, int]: 下载统计信息
        """
        pass
    
    @abstractmethod
    def download_artwork(self, artwork: Artwork) -> bool:
        """
        下载单个作品
        
        Args:
            artwork: 作品对象
            
        Returns:
            bool: 是否下载成功
        """
        pass
    
    @abstractmethod
    def is_downloaded(self, artwork_id: str) -> bool:
        """
        检查作品是否已下载
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            bool: 是否已下载
        """
        pass
    
    @abstractmethod
    def get_downloaded_path(self, artwork_id: str) -> Optional[str]:
        """
        获取已下载作品的路径
        
        Args:
            artwork_id: 作品ID
            
        Returns:
            Optional[str]: 作品路径
        """
        pass
    
    @abstractmethod
    def set_progress_callback(self, callback: Optional[Callable]) -> None:
        """
        设置进度回调函数
        
        Args:
            callback: 回调函数
        """
        pass
    
    @abstractmethod
    def set_status_callback(self, callback: Optional[Callable]) -> None:
        """
        设置状态回调函数
        
        Args:
            callback: 回调函数
        """
        pass
    
    @abstractmethod
    def stop_download(self) -> None:
        """停止下载"""
        pass
