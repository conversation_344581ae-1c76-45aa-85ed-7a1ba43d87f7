# 🚀 Pixiv Spider v6.0 启动指南

## 📋 快速启动

### 🎯 最简单的方式

1. **双击启动** (推荐)
   - Windows: 双击 `启动 Pixiv Spider.bat`
   - 或双击 `快速启动.bat` (已配置环境的用户)

2. **命令行启动**
   ```bash
   python start.py
   ```

### 🔧 首次使用

如果是第一次使用，请运行安装向导：

```bash
python install.py
```

安装向导会自动：
- ✅ 检查系统环境
- ✅ 安装Python依赖
- ✅ 安装前端依赖
- ✅ 构建前端应用
- ✅ 创建桌面快捷方式
- ✅ 测试安装结果

## 📦 系统要求

- **Python 3.8+**
- **Node.js 16+**
- **Windows 10+** / **macOS 10.14+** / **Linux**

## 🛠️ 手动安装

如果自动安装失败，可以手动执行：

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 安装前端依赖
```bash
cd electron-gui
npm install
npm run build
```

### 3. 启动应用
```bash
cd electron-gui
npm run app
```

## 🎯 启动方式对比

| 方式 | 适用场景 | 特点 |
|------|----------|------|
| `启动 Pixiv Spider.bat` | 首次使用/问题排查 | 完整检查，详细提示 |
| `快速启动.bat` | 日常使用 | 快速启动，无检查 |
| `python start.py` | 命令行用户 | 智能检查，跨平台 |
| `python install.py` | 首次安装 | 完整安装向导 |

## 🆘 常见问题

### 启动失败
1. 运行 `python install.py` 重新安装
2. 检查Python和Node.js是否正确安装
3. 运行 `python cleanup_project.py` 清理项目

### 界面异常
1. 重新构建前端：`cd electron-gui && npm run build`
2. 清理缓存：`python cleanup_project.py`

### 网络问题
1. 检查网络连接
2. 配置代理设置
3. 检查防火墙设置

## 💡 使用提示

- 🔐 首次启动需要登录Pixiv账号
- ⚙️ 可在界面中配置下载选项
- 📊 支持实时进度监控
- 🔄 支持断点续传
- 📁 自动分类保存

## 📞 获取帮助

- 📖 查看完整README.md
- 🧹 运行清理工具：`python cleanup_project.py`
- 🔧 重新安装：`python install.py`

---

**享受使用Pixiv Spider v6.0！** 🎉
