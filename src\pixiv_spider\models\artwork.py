"""
作品相关的数据模型
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class ArtworkType(Enum):
    """作品类型枚举"""
    ILLUSTRATION = "illustration"
    MANGA = "manga"
    UGOIRA = "ugoira"


class ArtworkStatus(Enum):
    """作品状态枚举"""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class ArtworkPage:
    """作品页面数据模型"""
    url_original: str
    url_medium: str
    url_square_medium: str
    width: int
    height: int
    page_number: int = 0
    
    @property
    def aspect_ratio(self) -> float:
        """获取宽高比"""
        return self.width / self.height if self.height > 0 else 1.0
    
    @property
    def resolution(self) -> str:
        """获取分辨率字符串"""
        return f"{self.width}x{self.height}"


@dataclass
class Artwork:
    """作品数据模型"""
    id: int
    title: str
    author_id: int
    author_name: str
    type: ArtworkType
    pages: List[ArtworkPage] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    description: str = ""
    view_count: int = 0
    like_count: int = 0
    bookmark_count: int = 0
    create_date: Optional[datetime] = None
    upload_date: Optional[datetime] = None
    status: ArtworkStatus = ArtworkStatus.PENDING
    local_path: Optional[str] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def page_count(self) -> int:
        """获取页面数量"""
        return len(self.pages)
    
    @property
    def is_single_page(self) -> bool:
        """是否为单页作品"""
        return self.page_count == 1
    
    @property
    def is_multi_page(self) -> bool:
        """是否为多页作品"""
        return self.page_count > 1
    
    @property
    def url(self) -> str:
        """获取作品URL"""
        return f"https://www.pixiv.net/artworks/{self.id}"
    
    @property
    def author_url(self) -> str:
        """获取作者URL"""
        return f"https://www.pixiv.net/users/{self.author_id}"
    
    def add_page(self, page: ArtworkPage) -> None:
        """添加页面"""
        page.page_number = len(self.pages)
        self.pages.append(page)
    
    def get_main_image_url(self) -> str:
        """获取主图片URL"""
        return self.pages[0].url_original if self.pages else ""
    
    def get_filename(self, page_number: int = 0, include_extension: bool = True) -> str:
        """生成文件名"""
        base_name = f"{self.title}_{self.id}"
        if self.is_multi_page:
            base_name += f"_p{page_number}"
        
        if include_extension and self.pages:
            if page_number < len(self.pages):
                url = self.pages[page_number].url_original
                extension = url.split('.')[-1] if '.' in url else 'jpg'
                base_name += f".{extension}"
        
        # 清理文件名中的非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            base_name = base_name.replace(char, '_')
        
        return base_name
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'title': self.title,
            'author_id': self.author_id,
            'author_name': self.author_name,
            'type': self.type.value,
            'pages': [
                {
                    'url_original': page.url_original,
                    'url_medium': page.url_medium,
                    'url_square_medium': page.url_square_medium,
                    'width': page.width,
                    'height': page.height,
                    'page_number': page.page_number
                }
                for page in self.pages
            ],
            'tags': self.tags,
            'description': self.description,
            'view_count': self.view_count,
            'like_count': self.like_count,
            'bookmark_count': self.bookmark_count,
            'create_date': self.create_date.isoformat() if self.create_date else None,
            'upload_date': self.upload_date.isoformat() if self.upload_date else None,
            'status': self.status.value,
            'local_path': self.local_path,
            'error_message': self.error_message,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Artwork':
        """从字典创建实例"""
        artwork = cls(
            id=data['id'],
            title=data['title'],
            author_id=data['author_id'],
            author_name=data['author_name'],
            type=ArtworkType(data['type']),
            tags=data.get('tags', []),
            description=data.get('description', ''),
            view_count=data.get('view_count', 0),
            like_count=data.get('like_count', 0),
            bookmark_count=data.get('bookmark_count', 0),
            status=ArtworkStatus(data.get('status', 'pending')),
            local_path=data.get('local_path'),
            error_message=data.get('error_message'),
            metadata=data.get('metadata', {})
        )
        
        # 转换日期
        if data.get('create_date'):
            artwork.create_date = datetime.fromisoformat(data['create_date'])
        if data.get('upload_date'):
            artwork.upload_date = datetime.fromisoformat(data['upload_date'])
        
        # 添加页面
        for page_data in data.get('pages', []):
            page = ArtworkPage(
                url_original=page_data['url_original'],
                url_medium=page_data['url_medium'],
                url_square_medium=page_data['url_square_medium'],
                width=page_data['width'],
                height=page_data['height'],
                page_number=page_data['page_number']
            )
            artwork.pages.append(page)
        
        return artwork 