<template>
  <el-container class="app-container" :class="{ 'dark-theme': isDarkMode }">
    <!-- 自定义标题栏 -->
    <div class="custom-titlebar">
      <div class="titlebar-content">
        <div class="titlebar-left">
          <el-icon class="app-icon"><Picture /></el-icon>
          <span class="app-name">Pixiv Spider</span>
        </div>
        <div class="titlebar-center">
          <!-- 空白区域，用于拖拽 -->
        </div>
        <div class="titlebar-right">
          <!-- 主题切换按钮 -->
          <el-button-group class="theme-switcher">
            <el-button
              :type="currentTheme === 'light' ? 'primary' : ''"
              @click="setTheme('light')"
              size="small"
            >
              <el-icon><Sunny /></el-icon>
              浅色
            </el-button>
            <el-button
              :type="currentTheme === 'dark' ? 'primary' : ''"
              @click="setTheme('dark')"
              size="small"
            >
              <el-icon><Moon /></el-icon>
              深色
            </el-button>
            <el-button
              :type="currentTheme === 'auto' ? 'primary' : ''"
              @click="setTheme('auto')"
              size="small"
            >
              <el-icon><Monitor /></el-icon>
              自动
            </el-button>
          </el-button-group>

          <el-button type="primary" @click="saveSettings" size="small">
            <el-icon><DocumentAdd /></el-icon>
            保存设置
          </el-button>
          <!-- 窗口控制按钮 -->
          <div class="window-controls">
            <button class="window-control minimize" @click="minimizeWindow">
              <el-icon><Minus /></el-icon>
            </button>
            <button class="window-control maximize" @click="maximizeWindow">
              <el-icon><FullScreen /></el-icon>
            </button>
            <button class="window-control close" @click="closeWindow">
              <el-icon><Close /></el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主体内容 -->
    <el-container class="main-container">
      <!-- 左侧配置面板 -->
      <el-aside width="400px" class="config-panel">
        <el-scrollbar>
          <div class="config-content">
            <!-- 登录状态 -->
            <LoginStatus />

            <!-- 下载设置 -->
            <DownloadSettings />

            <!-- 性能设置 -->
            <PerformanceSettings />

            <!-- 控制按钮 -->
            <ControlButtons />
          </div>
        </el-scrollbar>
      </el-aside>

      <!-- 右侧主要内容 -->
      <el-main class="main-content">
        <el-container direction="vertical">
          <!-- 进度和统计 -->
          <div class="progress-section">
            <ProgressDisplay />
            <StatsDisplay />
          </div>

          <!-- 日志区域 -->
          <div class="log-section">
            <LogDisplay />
          </div>
        </el-container>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import {
  Picture,
  Sunny,
  Moon,
  Monitor,
  DocumentAdd,
  Minus,
  FullScreen,
  Close
} from '@element-plus/icons-vue'
import LoginStatus from '../components/LoginStatus.vue'
import DownloadSettings from '../components/DownloadSettings.vue'
import PerformanceSettings from '../components/PerformanceSettings.vue'
import ControlButtons from '../components/ControlButtons.vue'
import ProgressDisplay from '../components/ProgressDisplay.vue'
import StatsDisplay from '../components/StatsDisplay.vue'
import LogDisplay from '../components/LogDisplay.vue'

export default {
  name: 'Home',
  components: {
    Picture,
    Sunny,
    Moon,
    Monitor,
    DocumentAdd,
    Minus,
    FullScreen,
    Close,
    LoginStatus,
    DownloadSettings,
    PerformanceSettings,
    ControlButtons,
    ProgressDisplay,
    StatsDisplay,
    LogDisplay
  },
  computed: {
    currentTheme() {
      return this.$store.state.theme
    },
    isDarkMode() {
      if (this.currentTheme === 'auto') {
        return this.getSystemTheme() === 'dark'
      }
      return this.currentTheme === 'dark'
    },
    isAutoTheme() {
      return this.currentTheme === 'auto'
    }
  },
  async mounted() {
    // 加载设置
    await this.$store.dispatch('loadSettings')
    this.loadThemeSettings()
    this.setupSystemThemeListener()
  },
  methods: {
    async saveSettings() {
      try {
        console.log('🔄 开始保存设置...')
        await this.$store.dispatch('saveSettings')
        this.$message.success('设置已保存')
        console.log('✅ 设置保存成功')
      } catch (error) {
        console.error('❌ 保存设置失败:', error)
        this.$message.error('保存设置失败: ' + error.message)
      }
    },

    setTheme(theme) {
      console.log('🎨 setTheme 被调用，参数:', theme)
      console.log('🎨 当前store状态:', this.$store.state.theme)
      this.$store.commit('setTheme', theme)
      console.log('🎨 commit后store状态:', this.$store.state.theme)
      this.$store.dispatch('saveSettings')
      this.applyTheme()

      // 显示主题切换反馈
      this.$message.success(`主题已切换为: ${theme}`)
    },

    getSystemTheme() {
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark'
      }
      return 'light'
    },

    setupSystemThemeListener() {
      if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', () => {
          if (this.currentTheme === 'auto') {
            this.applyTheme()
          }
        })
      }
    },

    applyTheme() {
      let themeToApply = 'light'

      if (this.currentTheme === 'dark') {
        themeToApply = 'dark'
      } else if (this.currentTheme === 'light') {
        themeToApply = 'light'
      } else if (this.currentTheme === 'auto') {
        themeToApply = this.getSystemTheme()
      }

      console.log('🎨 应用主题:', themeToApply, '当前主题设置:', this.currentTheme)

      // 应用主题到所有元素
      document.documentElement.setAttribute('data-theme', themeToApply)
      document.body.setAttribute('data-theme', themeToApply)

      // 移除所有主题类名
      document.body.classList.remove('dark-theme', 'light-theme', 'dark')
      document.documentElement.classList.remove('dark-theme', 'light-theme', 'dark')

      // 添加对应的主题类名
      if (themeToApply === 'dark') {
        document.body.classList.add('dark-theme', 'dark')
        document.documentElement.classList.add('dark-theme', 'dark')
      } else {
        document.body.classList.add('light-theme')
        document.documentElement.classList.add('light-theme')
      }

      // 强制重新渲染并验证
      this.$nextTick(() => {
        const actualTheme = document.documentElement.getAttribute('data-theme')
        console.log('✅ 主题应用完成，实际data-theme属性:', actualTheme)
        console.log('🔍 body背景色:', window.getComputedStyle(document.body).backgroundColor)

        // 更新窗口标题以显示当前主题（用于调试）
        if (window.electronAPI && window.electronAPI.setTitle) {
          window.electronAPI.setTitle(`Pixiv Spider - ${actualTheme} 主题`)
        }
      })
    },

    loadThemeSettings() {
      // 主题设置现在由store管理，在main.js中加载
      this.applyTheme()
    },

    // 窗口控制方法
    minimizeWindow() {
      if (window.electronAPI && window.electronAPI.minimizeWindow) {
        window.electronAPI.minimizeWindow()
      }
    },

    maximizeWindow() {
      if (window.electronAPI && window.electronAPI.maximizeWindow) {
        window.electronAPI.maximizeWindow()
      }
    },

    closeWindow() {
      if (window.electronAPI && window.electronAPI.closeWindow) {
        window.electronAPI.closeWindow()
      }
    }
  }
}
</script>

<style scoped>
/* 基础布局 */
.app-container {
  height: 100vh;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* 自定义标题栏样式 */
.custom-titlebar {
  height: 40px;
  background: var(--bg-primary);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  -webkit-app-region: drag;
  user-select: none;
  box-shadow: 0 2px 8px var(--shadow);
  transition: background-color 0.3s ease;
  z-index: 1000;
  border-bottom: 1px solid var(--border-color);
}

.titlebar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 10px;
}

.titlebar-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
}

.app-icon {
  font-size: 16px;
}

.app-name {
  font-size: 14px;
}

.titlebar-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-app-region: drag;
}

.titlebar-right {
  display: flex;
  align-items: center;
  gap: 10px;
  -webkit-app-region: no-drag;
}

.theme-switcher {
  margin: 0;
}

.window-controls {
  display: flex;
  height: 100%;
}

.window-control {
  width: 46px;
  height: 100%;
  border: none;
  background: transparent;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.window-control:hover {
  background-color: var(--bg-tertiary);
}

.window-control.close:hover {
  background-color: var(--error-color);
}

/* 主容器布局 */
.main-container {
  flex: 1;
  height: calc(100vh - 40px);
}

/* 统一主题样式 - 使用CSS变量 */
.config-panel {
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  transition: all 0.3s ease;
  height: 100%;
}

.config-content {
  padding: 20px;
}

.main-content {
  padding: 20px;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease;
  height: 100%;
}

.progress-section {
  margin-bottom: 20px;
}

.log-section {
  flex: 1;
  min-height: 0;
}

/* 重复的标题栏样式已删除，使用上面的定义 */

/* Windows 11 风格调整 */
.app-container {
  border-radius: 8px;
  overflow: hidden;
}

.app-header {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .theme-switcher {
    margin-right: 0;
    margin-bottom: 5px;
  }

  .app-title {
    font-size: 20px;
  }
}
</style>
