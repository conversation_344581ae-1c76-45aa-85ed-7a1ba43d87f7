const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron')

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 存储相关
  getStoreValue: (key) => ipcRenderer.invoke('get-store-value', key),
  setStoreValue: (key, value) => ipcRenderer.invoke('set-store-value', key, value),

  // 对话框相关
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),

  // 窗口控制相关
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  setTitle: (title) => ipcRenderer.invoke('set-window-title', title),

  // === 新的IPC API ===

  // 认证相关
  getAuthStatus: () => ipcRenderer.invoke('api-auth-status'),
  login: (forceRelogin = false) => ipcRenderer.invoke('api-auth-login', forceRelogin),
  checkCookies: () => ipcRenderer.invoke('api-auth-check-cookies'),
  startSeleniumLogin: () => ipcRenderer.invoke('api-auth-start-selenium'),
  confirmLogin: () => ipcRenderer.invoke('api-auth-confirm-login'),
  cancelLogin: () => ipcRenderer.invoke('api-auth-cancel-login'),

  // 下载相关
  startDownload: (settings) => {
    try {
      console.log('🔌 Preload: 准备调用ipcRenderer.invoke')
      console.log('🔌 Preload: 传递的设置:', settings)

      // 验证设置是否可序列化
      JSON.stringify(settings)
      console.log('✅ Preload: 设置序列化验证通过')

      return ipcRenderer.invoke('api-download-start', settings)
    } catch (error) {
      console.error('❌ Preload: startDownload失败:', error)
      throw error
    }
  },
  stopDownload: () => ipcRenderer.invoke('api-download-stop'),
  pauseDownload: () => ipcRenderer.invoke('api-download-pause'),
  resumeDownload: () => ipcRenderer.invoke('api-download-resume'),
  getDownloadStatus: () => ipcRenderer.invoke('api-download-status'),

  // 配置相关
  getConfig: () => ipcRenderer.invoke('api-config-get'),
  updateConfig: (config) => ipcRenderer.invoke('api-config-update', config),

  // 健康检查
  healthCheck: () => ipcRenderer.invoke('api-health-check'),

  // === 事件监听 ===

  // 后端状态事件
  onBackendReady: (callback) => ipcRenderer.on('backend-ready', callback),
  onBackendError: (callback) => ipcRenderer.on('backend-error', callback),

  // 状态更新事件
  onStatusUpdate: (callback) => ipcRenderer.on('status-update', callback),
  onProgressUpdate: (callback) => ipcRenderer.on('progress-update', callback),
  onDownloadComplete: (callback) => ipcRenderer.on('download-complete', callback),
  onAuthStatus: (callback) => ipcRenderer.on('auth-status', callback),

  // 监听Python后端日志（保留兼容性）
  onPythonLog: (callback) => ipcRenderer.on('python-log', callback),
  onPythonError: (callback) => ipcRenderer.on('python-error', callback),

  // 新的分级日志监听
  onPythonInfo: (callback) => ipcRenderer.on('python-info', callback),
  onPythonWarning: (callback) => ipcRenderer.on('python-warning', callback),

  // 监听详细日志消息
  onLogMessage: (callback) => ipcRenderer.on('log-message', callback),

  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  removeListener: (channel, callback) => ipcRenderer.removeListener(channel, callback)
})
