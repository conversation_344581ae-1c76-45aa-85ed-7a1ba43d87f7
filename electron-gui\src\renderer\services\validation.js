/**
 * 统一验证服务
 * 解决前后端重复的验证逻辑
 */

class ValidationService {
  constructor() {
    this.errorMessages = {
      // 通用错误
      REQUIRED_FIELD: '此字段为必填项',
      INVALID_NUMBER: '请输入有效的数字',
      INVALID_RANGE: '数值超出有效范围',
      
      // 路径相关
      INVALID_PATH: '请选择有效的下载路径',
      PATH_NOT_WRITABLE: '下载路径无写入权限',
      
      // 页码相关
      INVALID_PAGE_RANGE: '起始页码不能大于结束页码',
      PAGE_TOO_SMALL: '页码必须大于0',
      PAGE_TOO_LARGE: '页码不能超过1000',
      
      // 搜索相关
      SEARCH_KEYWORD_REQUIRED: '搜索模式下请输入搜索关键词',
      KEYWORD_TOO_SHORT: '搜索关键词至少需要1个字符',
      KEYWORD_TOO_LONG: '搜索关键词不能超过100个字符',
      
      // 画师相关
      ARTIST_ID_REQUIRED: '画师模式下请输入画师ID',
      INVALID_ARTIST_ID: '请输入有效的画师ID（正整数）',
      
      // 认证相关
      LOGIN_REQUIRED: '请先登录Pixiv账号',
      AUTH_EXPIRED: '登录已过期，请重新登录',
      
      // 收藏相关
      INVALID_BOOKMARK_COUNT: '收藏数必须为正整数',
      BOOKMARK_RANGE_ERROR: '最小收藏数不能大于最大收藏数'
    }
  }

  /**
   * 验证下载配置
   */
  validateDownloadConfig(config) {
    const errors = []

    // 验证基础路径
    if (!config.save_path) {
      errors.push(this.errorMessages.INVALID_PATH)
    }

    // 根据下载模式验证
    switch (config.download_mode) {
      case 'search':
        errors.push(...this._validateSearchConfig(config))
        break
      case 'user':
        errors.push(...this._validateUserConfig(config))
        break
      case 'date':
        errors.push(...this._validateDateConfig(config))
        break
      case 'ranking':
        errors.push(...this._validateRankingConfig(config))
        break
    }

    return errors
  }

  /**
   * 验证搜索配置
   */
  _validateSearchConfig(config) {
    const errors = []

    if (!config.search_keyword) {
      errors.push(this.errorMessages.SEARCH_KEYWORD_REQUIRED)
    } else {
      if (config.search_keyword.length < 1) {
        errors.push(this.errorMessages.KEYWORD_TOO_SHORT)
      }
      if (config.search_keyword.length > 100) {
        errors.push(this.errorMessages.KEYWORD_TOO_LONG)
      }
    }

    // 验证页码范围
    errors.push(...this._validatePageRange(config.start_page, config.end_page))

    // 验证收藏过滤
    if (config.min_bookmarks < 0) {
      errors.push(this.errorMessages.INVALID_BOOKMARK_COUNT)
    }

    return errors
  }

  /**
   * 验证画师配置
   */
  _validateUserConfig(config) {
    const errors = []

    if (!config.user_id || config.user_id <= 0) {
      errors.push(this.errorMessages.ARTIST_ID_REQUIRED)
    } else if (!Number.isInteger(config.user_id)) {
      errors.push(this.errorMessages.INVALID_ARTIST_ID)
    }

    // 验证页码范围
    errors.push(...this._validatePageRange(config.start_page, config.end_page))

    return errors
  }

  /**
   * 验证关注配置
   */
  _validateDateConfig(config) {
    const errors = []

    if (config.date_mode === 'by_page_range') {
      errors.push(...this._validatePageRange(config.start_page, config.end_page))
    } else if (config.date_mode === 'by_date_range') {
      if (config.days <= 0) {
        errors.push('天数必须大于0')
      }
    }

    return errors
  }

  /**
   * 验证排行榜配置
   */
  _validateRankingConfig(config) {
    const errors = []
    // 排行榜模式通常使用固定配置，验证较少
    return errors
  }

  /**
   * 验证页码范围
   */
  _validatePageRange(startPage, endPage) {
    const errors = []

    if (startPage < 1) {
      errors.push(this.errorMessages.PAGE_TOO_SMALL)
    }

    if (endPage < 1) {
      errors.push(this.errorMessages.PAGE_TOO_SMALL)
    }

    if (startPage > endPage) {
      errors.push(this.errorMessages.INVALID_PAGE_RANGE)
    }

    if (startPage > 1000 || endPage > 1000) {
      errors.push(this.errorMessages.PAGE_TOO_LARGE)
    }

    return errors
  }

  /**
   * 验证用户认证状态
   */
  validateAuthStatus(isLoggedIn) {
    if (!isLoggedIn) {
      return [this.errorMessages.LOGIN_REQUIRED]
    }
    return []
  }

  /**
   * 验证路径
   */
  validatePath(path) {
    const errors = []

    if (!path || path.trim() === '') {
      errors.push(this.errorMessages.INVALID_PATH)
    }

    // 简单的路径格式验证
    if (path && !/^[a-zA-Z]:[\\\/]/.test(path) && !/^\//.test(path)) {
      errors.push('请输入有效的文件路径')
    }

    return errors
  }

  /**
   * 验证数字范围
   */
  validateNumberRange(value, min, max, fieldName = '数值') {
    const errors = []

    if (typeof value !== 'number' || isNaN(value)) {
      errors.push(`${fieldName}必须是有效数字`)
      return errors
    }

    if (value < min) {
      errors.push(`${fieldName}不能小于${min}`)
    }

    if (value > max) {
      errors.push(`${fieldName}不能大于${max}`)
    }

    return errors
  }

  /**
   * 获取错误消息
   */
  getErrorMessage(errorCode) {
    return this.errorMessages[errorCode] || '未知错误'
  }

  /**
   * 格式化错误列表
   */
  formatErrors(errors) {
    if (!errors || errors.length === 0) {
      return ''
    }

    if (errors.length === 1) {
      return errors[0]
    }

    return errors.map((error, index) => `${index + 1}. ${error}`).join('\n')
  }

  /**
   * 检查是否有错误
   */
  hasErrors(errors) {
    return errors && errors.length > 0
  }
}

// 创建单例实例
const validationService = new ValidationService()

export default validationService
