"""
自定义异常类
"""


class PixivSpiderError(Exception):
    """Pixiv爬虫基础异常类"""
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class AuthenticationError(PixivSpiderError):
    """认证相关异常"""
    def __init__(self, message: str = "认证失败"):
        super().__init__(message, "AUTH_ERROR")


class NetworkError(PixivSpiderError):
    """网络相关异常"""
    def __init__(self, message: str = "网络请求失败"):
        super().__init__(message, "NETWORK_ERROR")


class DownloadError(PixivSpiderError):
    """下载相关异常"""
    def __init__(self, message: str = "下载失败"):
        super().__init__(message, "DOWNLOAD_ERROR")


class ParseError(PixivSpiderError):
    """解析相关异常"""
    def __init__(self, message: str = "解析失败"):
        super().__init__(message, "PARSE_ERROR")


class ConfigError(PixivSpiderError):
    """配置相关异常"""
    def __init__(self, message: str = "配置错误"):
        super().__init__(message, "CONFIG_ERROR")


class ValidationError(PixivSpiderError):
    """验证相关异常"""
    def __init__(self, message: str = "验证失败"):
        super().__init__(message, "VALIDATION_ERROR")


class RateLimitError(PixivSpiderError):
    """速率限制异常"""
    def __init__(self, message: str = "请求频率过高"):
        super().__init__(message, "RATE_LIMIT_ERROR")


class SeleniumError(PixivSpiderError):
    """Selenium相关异常"""
    def __init__(self, message: str = "浏览器操作失败"):
        super().__init__(message, "SELENIUM_ERROR") 