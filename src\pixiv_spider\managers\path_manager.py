"""
路径管理器模块

负责管理不同下载模式的路径处理
"""

import os
import logging
from typing import Dict, Any

from ..models.config import DownloadConfig, DownloadMode


class PathManager:
    """路径管理器"""

    def __init__(self, download_config: DownloadConfig):
        """
        初始化路径管理器
        
        Args:
            download_config: 下载配置
        """
        self.logger = logging.getLogger(__name__)
        self.download_config = download_config

    def get_download_path_for_mode(self) -> str:
        """根据下载模式获取对应的下载路径"""
        mode = self.download_config.download_mode

        if mode == DownloadMode.RANKING:
            return self.get_ranking_save_path()
        elif mode == DownloadMode.SEARCH:
            return self.get_search_save_path()
        elif mode == DownloadMode.USER:
            return self.get_user_save_path()
        else:
            # 其他模式使用默认路径
            return self.download_config.save_path

    def get_ranking_save_path(self) -> str:
        """获取排行榜的保存路径"""
        ranking_config = self.download_config.ranking_config
        
        # 使用自定义路径或默认路径
        base_path = ranking_config.custom_save_path or self.download_config.save_path
        
        # 如果启用自动命名，添加分类文件夹
        if ranking_config.auto_folder_naming:
            folder_name = ranking_config.get_folder_name()
            save_path = os.path.join(base_path, folder_name)
        else:
            save_path = base_path
        
        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)
        
        return save_path

    def get_search_save_path(self) -> str:
        """获取搜索的保存路径"""
        # 使用搜索专用路径或默认路径
        if self.download_config.search_save_path:
            save_path = self.download_config.search_save_path
        else:
            save_path = self.download_config.save_path

        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)

        return save_path

    def get_user_save_path(self) -> str:
        """获取用户的保存路径"""
        # 使用用户专用路径或默认路径
        if self.download_config.user_save_path:
            save_path = self.download_config.user_save_path
        else:
            save_path = self.download_config.save_path

        # 确保目录存在
        os.makedirs(save_path, exist_ok=True)

        return save_path

    def ensure_download_directory(self, download_path: str) -> str:
        """确保下载目录存在并返回有效路径"""
        # 确保路径编码正确
        try:
            # 尝试正确编码路径用于日志显示
            safe_path = download_path.encode('utf-8').decode('utf-8')
            self.logger.info(f"📁 下载路径: {safe_path}")
        except (UnicodeEncodeError, UnicodeDecodeError):
            # 如果编码有问题，使用repr显示
            self.logger.info(f"📁 下载路径 (编码问题): {repr(download_path)}")

        # 验证并创建下载目录
        try:
            # 确保下载目录存在
            if not os.path.exists(download_path):
                os.makedirs(download_path, exist_ok=True)
                self.logger.info(f"✅ 创建下载目录成功: {download_path}")
            else:
                self.logger.info(f"✅ 下载目录已存在: {download_path}")
            return download_path
        except Exception as e:
            self.logger.error(f"❌ 创建下载目录失败: {e}")
            # 尝试使用安全的路径
            safe_download_path = self.download_config.save_path.replace('关注画师', 'followed_artists')
            self.logger.info(f"🔄 使用备用路径: {safe_download_path}")
            try:
                os.makedirs(safe_download_path, exist_ok=True)
                self.logger.info(f"✅ 备用目录创建成功: {safe_download_path}")
                return safe_download_path
            except Exception as e2:
                self.logger.error(f"❌ 备用目录创建也失败: {e2}")
                # 使用默认路径
                default_path = 'H:\\Pixiv\\Downloads'
                os.makedirs(default_path, exist_ok=True)
                self.logger.info(f"🔄 使用默认路径: {default_path}")
                return default_path

    def update_download_service_path(self, download_service, new_path: str, original_path: str):
        """更新下载服务的路径配置"""
        if new_path != original_path:
            self.download_config.save_path = new_path
            if download_service and hasattr(download_service, 'download_config'):
                download_service.download_config.save_path = new_path
            self.logger.info(f"🔄 已更新下载配置路径: {new_path}")

    def restore_download_service_path(self, download_service, original_path: str):
        """恢复下载服务的原始路径配置"""
        self.download_config.save_path = original_path
        if download_service and hasattr(download_service, 'download_config'):
            download_service.download_config.save_path = original_path
