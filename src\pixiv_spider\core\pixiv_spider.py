"""
Pixiv爬虫核心类

精简的模块化设计，专注核心功能
"""

import logging
from typing import List, Optional, Callable, Dict, Any

from ..models.artwork import Artwork
from ..models.config import DownloadConfig, SpiderConfig
from ..models.exceptions import PixivSpiderError, AuthenticationError
from ..interfaces.auth_interface import IAuthService
from ..interfaces.api_interface import IApiService
from ..interfaces.download_interface import IDownloadService
from ..container.service_container import ServiceContainer
from ..container.service_registry import ServiceRegistry
from ..utils.selenium_utils import SeleniumDriver
from ..config.config_manager import ConfigManager

# 新的模块化组件
from ..utils.cache_manager import CacheManager
from ..processors.artwork_processor import ArtworkProcessor
from ..handlers.download_mode_handler import DownloadModeHandler
from ..managers.resource_manager import ResourceManager
from ..managers.path_manager import PathManager


class PixivSpider:
    """Pixiv爬虫主类 - 精简的模块化设计"""

    def __init__(self, container_or_config=None):
        """
        初始化爬虫

        Args:
            container_or_config: 服务容器或配置管理器，如果为None则创建默认实例
        """
        self.logger = logging.getLogger(__name__)

        # 处理不同类型的输入参数
        if isinstance(container_or_config, ConfigManager):
            # 如果传入的是ConfigManager，创建容器并注册
            self.config_manager = container_or_config
            self.container = ServiceContainer()
            self.registry = ServiceRegistry(self.container)
            # 将ConfigManager注册到容器中
            self.container.register_singleton('config_manager', self.config_manager)
        elif isinstance(container_or_config, ServiceContainer):
            # 如果传入的是ServiceContainer
            self.container = container_or_config
            self.registry = ServiceRegistry(self.container)
            self.config_manager = self.container.get_config_manager()
        else:
            # 如果为None或其他，创建默认实例
            self.container = ServiceContainer()
            self.registry = ServiceRegistry(self.container)
            self.config_manager = self.container.get_config_manager()

        self._services_registered = False

        # 加载配置
        self.download_config = self.config_manager.load_download_config()
        self.spider_config = self.config_manager.load_spider_config()

        # 初始化模块化组件
        self._init_modular_components()

        # 延迟初始化服务
        self.auth_service: Optional[IAuthService] = None
        self.api_service: Optional[IApiService] = None
        self.download_service: Optional[IDownloadService] = None
        self.selenium_driver: Optional[SeleniumDriver] = None
        
        # 状态
        self._is_authenticated = False
        self._cookies: Optional[Dict[str, Any]] = None

    def _init_modular_components(self):
        """初始化模块化组件"""
        # 缓存管理器
        max_cache_size = getattr(self.spider_config, 'max_cache_size', 1000)
        self.cache_manager = CacheManager(max_cache_size)
        
        # 资源管理器
        self.resource_manager = ResourceManager()
        self.resource_manager.register_cache_manager(self.cache_manager)
        
        # 路径管理器
        self.path_manager = PathManager(self.download_config)
        
        # 作品处理器和下载模式处理器将在服务初始化后创建
        self.artwork_processor: Optional[ArtworkProcessor] = None
        self.download_mode_handler: Optional[DownloadModeHandler] = None

    def _ensure_services_registered(self) -> None:
        """确保服务已注册（延迟初始化）"""
        if not self._services_registered:
            self.registry.register_all_services()
            self.auth_service = self.container.get('auth_service')
            self._services_registered = True
            self.logger.debug("服务注册完成")

    def _init_processors(self):
        """初始化处理器（需要在API服务创建后调用）"""
        if self.api_service and not self.artwork_processor:
            # 作品处理器
            self.artwork_processor = ArtworkProcessor(
                self.api_service, 
                self.download_config, 
                self.cache_manager
            )
            
            # 下载模式处理器
            if self.selenium_driver:
                self.download_mode_handler = DownloadModeHandler(
                    self.download_config,
                    self.selenium_driver,
                    self.artwork_processor
                )

    def set_progress_callback(self, callback: Callable) -> None:
        """设置进度回调函数"""
        self.resource_manager.set_progress_callback(callback)

    def set_status_callback(self, callback: Callable) -> None:
        """设置状态回调函数"""
        self.resource_manager.set_status_callback(callback)

    def authenticate(self) -> bool:
        """
        进行身份验证

        Returns:
            bool: 是否验证成功
        """
        try:
            # 确保服务已注册
            self._ensure_services_registered()

            # 尝试加载已保存的Cookie
            success, cookies = self.auth_service.check_login_status()
            
            if success and cookies:
                self._cookies = cookies
                self._setup_services_with_cookies(cookies)
                self._is_authenticated = True
                self.logger.info("使用已保存的Cookie登录成功")
                return True
            
            # 如果没有有效Cookie，需要重新登录
            self.logger.warning("未找到有效的登录信息，需要重新登录")
            return False
            
        except Exception as e:
            self.logger.error(f"身份验证失败: {e}")
            return False

    def interactive_login(self) -> bool:
        """
        交互式登录

        Returns:
            bool: 是否登录成功
        """
        try:
            # 确保服务已注册
            self._ensure_services_registered()

            success, cookies = self.auth_service.get_or_create_cookies()
            
            if success and cookies:
                self._cookies = cookies
                self._setup_services_with_cookies(cookies)
                self._is_authenticated = True
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"交互式登录失败: {e}")
            return False

    def _setup_services_with_cookies(self, cookies):
        """使用cookies设置服务"""
        # 使用服务注册器创建配置好的服务
        self.api_service = self.registry.create_configured_api_service(cookies)
        self.resource_manager.register_api_service(self.api_service)

        # 只在下载服务不存在时创建，避免重复扫描
        if not self.download_service:
            self.logger.info("🔧 创建下载服务...")
            self.download_service = self.registry.create_configured_download_service(self.api_service)
            self.resource_manager.register_download_service(self.download_service)
        else:
            # 更新API服务引用
            self.download_service.api_service = self.api_service
            self.logger.info("🔄 更新下载服务的API服务引用")

    def _ensure_selenium_driver(self):
        """确保Selenium驱动器已初始化"""
        self.logger.info(f"检查Selenium驱动器状态: 已存在={self.selenium_driver is not None}, 已认证={self._is_authenticated}")

        if not self.selenium_driver:
            # 即使未认证也尝试创建驱动器，让用户有机会在浏览器中登录
            cookies_to_use = self._cookies if self._is_authenticated else []

            self.logger.info(f"正在初始化Selenium驱动器... (使用Cookie: {len(cookies_to_use) > 0})")
            try:
                self.selenium_driver = SeleniumDriver(cookies_to_use, self.config_manager)
                self.resource_manager.register_selenium_driver(self.selenium_driver)
                self.logger.info("Selenium驱动器初始化成功")

                # 如果没有认证，给出提示但不阻止执行
                if not self._is_authenticated:
                    self.logger.warning("⚠️ 未找到保存的登录信息")
                    self.logger.warning("如果页面要求登录，请在浏览器中手动登录")
                    self.resource_manager._notify_status("⚠️ 未找到登录信息，可能需要在浏览器中登录")

            except Exception as e:
                self.logger.error(f"Selenium驱动器初始化失败: {e}")
                return False
        else:
            self.logger.info("Selenium驱动器已存在，跳过初始化")
        return True

    def start_download(self) -> Dict[str, Any]:
        """
        开始下载

        Returns:
            Dict[str, Any]: 下载结果统计
        """
        if self.resource_manager._is_running:
            raise PixivSpiderError("爬虫已在运行中")

        if not self._is_authenticated:
            # 尝试自动认证
            self.logger.info("未认证，尝试自动登录...")
            auth_result = self.authenticate()
            self.logger.info(f"自动认证结果: {auth_result}")
            if not auth_result:
                self.logger.warning("自动认证失败，将尝试在无认证状态下继续")
                self.logger.warning("如果遇到登录页面，请在浏览器中手动登录")
                self.resource_manager._notify_status("⚠️ 未找到登录信息，如需登录请在浏览器中操作")

        try:
            # 重置状态，确保干净的启动环境
            self.resource_manager.reset_for_new_download(self.registry, self._cookies)

            self.resource_manager.set_running_state(True)
            self.resource_manager._notify_status("开始下载...")

            # 验证配置
            errors = self.download_config.validate()
            if errors:
                raise PixivSpiderError(f"配置验证失败: {', '.join(errors)}")

            # 确保目录存在
            self.config_manager.ensure_directories()

            # 确保Selenium驱动器已初始化
            if not self._ensure_selenium_driver():
                raise PixivSpiderError("Selenium驱动器初始化失败")

            # 初始化处理器
            self._init_processors()

            # 根据下载模式执行不同的下载逻辑
            artworks = self.download_mode_handler.get_artworks_by_mode()

            if not artworks:
                self.resource_manager._notify_status("没有找到符合条件的作品")
                return {'total': 0, 'success': 0, 'failed': 0, 'skipped': 0}

            self.logger.info(f"找到 {len(artworks)} 个作品，开始下载")
            self.resource_manager._notify_status(f"找到 {len(artworks)} 个作品，开始下载...")

            # 设置下载路径并执行下载
            stats = self._execute_download_with_path(artworks)

            self.resource_manager._notify_status("下载任务完成")
            return stats

        except Exception as e:
            self.logger.error(f"下载过程中发生错误: {e}")
            self.resource_manager._notify_status(f"下载失败: {e}")
            raise
        finally:
            self.resource_manager.set_running_state(False)

    def _execute_download_with_path(self, artworks: List[Artwork]) -> Dict[str, Any]:
        """
        使用正确的路径执行下载

        Args:
            artworks: 作品列表

        Returns:
            Dict[str, Any]: 下载统计信息
        """
        download_path = self.path_manager.get_download_path_for_mode()

        # 确保下载目录存在
        validated_path = self.path_manager.ensure_download_directory(download_path)

        # 临时修改路径配置
        original_save_path = self.download_config.save_path
        try:
            self.path_manager.update_download_service_path(
                self.download_service, validated_path, original_save_path
            )

            self.logger.info(f"🚀 开始批量下载 {len(artworks)} 个作品...")

            try:
                self.logger.info("🔧 正在调用 batch_download 方法...")
                result = self.download_service.batch_download(artworks)
                self.logger.info(f"🔧 batch_download 方法返回: {result}")

                # 下载完成后自动清理Selenium资源
                self.logger.info("🧹 下载完成，开始清理Selenium资源...")
                self.resource_manager.cleanup_selenium_after_download()

                return result
            except Exception as e:
                self.logger.error(f"❌ batch_download 方法异常: {e}")
                import traceback
                self.logger.error(f"❌ 异常堆栈: {traceback.format_exc()}")

                # 异常情况下也要清理Selenium资源
                self.logger.info("🧹 异常情况下清理Selenium资源...")
                self.resource_manager.cleanup_selenium_after_download()

                raise
        finally:
            # 恢复原始路径
            self.path_manager.restore_download_service_path(self.download_service, original_save_path)

    def stop_download(self) -> None:
        """停止下载并立即清理资源"""
        self.resource_manager.stop_all_services()

    def get_download_stats(self) -> dict:
        """获取下载统计信息"""
        return {
            "is_running": self.resource_manager._is_running,
            "is_authenticated": self._is_authenticated,
            "download_mode": self.download_config.download_mode.value,
            "save_path": self.download_config.save_path,
            **self.resource_manager.get_resource_stats()
        }

    def update_download_config(self, config: DownloadConfig) -> None:
        """更新下载配置"""
        self.download_config = config
        self.config_manager.save_download_config(config)

        # 更新路径管理器的配置
        self.path_manager.download_config = config

        # 更新下载服务的配置
        if self.download_service:
            self.download_service.download_config = config

        self.logger.info("下载配置已更新")

    def update_spider_config(self, config: SpiderConfig) -> None:
        """更新爬虫配置"""
        self.spider_config = config
        self.config_manager.save_spider_config(config)
        self.logger.info("爬虫配置已更新")

    def cleanup(self):
        """清理资源"""
        self.resource_manager.cleanup_all_resources()

    def __enter__(self):
        """支持上下文管理器"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持上下文管理器"""
        self.cleanup()
        # 不抑制异常
        return False
