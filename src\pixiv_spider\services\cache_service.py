"""
缓存服务

负责数据缓存和管理
"""

import pickle
import json
import time
import logging
from typing import Any, Optional, Dict
from pathlib import Path


class CacheService:
    """缓存服务类"""
    
    def __init__(self, cache_dir: str = "cache"):
        """
        初始化缓存服务
        
        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 内存缓存
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
    
    def set(self, key: str, value: Any, expire_time: Optional[int] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            expire_time: 过期时间（秒），None表示不过期
            
        Returns:
            bool: 是否成功
        """
        try:
            cache_data = {
                'value': value,
                'timestamp': time.time(),
                'expire_time': expire_time
            }
            
            # 保存到内存缓存
            self._memory_cache[key] = cache_data
            
            # 保存到文件缓存
            cache_file = self.cache_dir / f"{key}.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置缓存失败: {key}, 错误: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            Any: 缓存值或默认值
        """
        try:
            # 先检查内存缓存
            if key in self._memory_cache:
                cache_data = self._memory_cache[key]
                if self._is_valid(cache_data):
                    return cache_data['value']
                else:
                    # 过期，删除
                    del self._memory_cache[key]
            
            # 检查文件缓存
            cache_file = self.cache_dir / f"{key}.pkl"
            if cache_file.exists():
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                if self._is_valid(cache_data):
                    # 重新加载到内存缓存
                    self._memory_cache[key] = cache_data
                    return cache_data['value']
                else:
                    # 过期，删除文件
                    cache_file.unlink()
            
            return default
            
        except Exception as e:
            self.logger.error(f"获取缓存失败: {key}, 错误: {e}")
            return default
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否成功
        """
        try:
            # 从内存缓存删除
            if key in self._memory_cache:
                del self._memory_cache[key]
            
            # 从文件缓存删除
            cache_file = self.cache_dir / f"{key}.pkl"
            if cache_file.exists():
                cache_file.unlink()
            
            return True
            
        except Exception as e:
            self.logger.error(f"删除缓存失败: {key}, 错误: {e}")
            return False
    
    def clear(self) -> bool:
        """
        清空所有缓存
        
        Returns:
            bool: 是否成功
        """
        try:
            # 清空内存缓存
            self._memory_cache.clear()
            
            # 清空文件缓存
            for cache_file in self.cache_dir.glob("*.pkl"):
                cache_file.unlink()
            
            return True
            
        except Exception as e:
            self.logger.error(f"清空缓存失败: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否存在
        """
        # 检查内存缓存
        if key in self._memory_cache:
            if self._is_valid(self._memory_cache[key]):
                return True
            else:
                del self._memory_cache[key]
        
        # 检查文件缓存
        cache_file = self.cache_dir / f"{key}.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                if self._is_valid(cache_data):
                    return True
                else:
                    cache_file.unlink()
            except Exception:
                pass
        
        return False
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            Dict: 缓存统计信息
        """
        memory_count = len(self._memory_cache)
        file_count = len(list(self.cache_dir.glob("*.pkl")))
        
        total_size = 0
        for cache_file in self.cache_dir.glob("*.pkl"):
            try:
                total_size += cache_file.stat().st_size
            except Exception:
                continue
        
        return {
            'memory_cache_count': memory_count,
            'file_cache_count': file_count,
            'total_size_bytes': total_size,
            'cache_dir': str(self.cache_dir)
        }
    
    def _is_valid(self, cache_data: Dict[str, Any]) -> bool:
        """
        检查缓存数据是否有效
        
        Args:
            cache_data: 缓存数据
            
        Returns:
            bool: 是否有效
        """
        if 'expire_time' not in cache_data or 'timestamp' not in cache_data:
            return False
        
        expire_time = cache_data['expire_time']
        if expire_time is None:
            return True  # 永不过期
        
        current_time = time.time()
        timestamp = cache_data['timestamp']
        
        return current_time - timestamp < expire_time 