<template>
  <el-card class="performance-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Cpu /></el-icon>
        <span>性能设置</span>
      </div>
    </template>
    
    <el-form :model="settings" label-width="80px" size="small">
      <!-- 并发数 -->
      <el-form-item label="并发数">
        <div class="slider-container">
          <el-slider
            v-model="settings.maxConcurrent"
            :min="1"
            :max="10"
            :step="1"
            show-stops
            show-input
            :show-input-controls="false"
            @change="updateSettings"
          />
          <div class="slider-tip">
            <el-text size="small" type="info">
              建议值: 3-5，过高可能被限制
            </el-text>
          </div>
        </div>
      </el-form-item>

      <!-- 请求延迟 -->
      <el-form-item label="请求延迟">
        <div class="slider-container">
          <el-slider
            v-model="settings.delay"
            :min="500"
            :max="5000"
            :step="100"
            show-input
            :show-input-controls="false"
            :format-tooltip="formatDelay"
            @change="updateSettings"
          />
          <div class="slider-tip">
            <el-text size="small" type="info">
              {{ formatDelay(settings.delay) }}，避免请求过于频繁
            </el-text>
          </div>
        </div>
      </el-form-item>

      <!-- 重试次数 -->
      <el-form-item label="重试次数">
        <el-input-number
          v-model="settings.retryCount"
          :min="0"
          :max="10"
          style="width: 100%"
          @change="updateSettings"
        />
      </el-form-item>
      
      <!-- 超时时间 -->
      <el-form-item label="超时时间">
        <div class="timeout-input">
          <el-input-number
            v-model="settings.timeout"
            :min="5"
            :max="120"
            style="width: 100%"
            @change="updateSettings"
          />
          <span class="unit">秒</span>
        </div>
      </el-form-item>

      <!-- 高级选项 -->
      <el-divider content-position="left">
        <el-text size="small">高级选项</el-text>
      </el-divider>

      <el-form-item label="跳过已存在">
        <el-switch
          v-model="settings.skipExisting"
          active-text="是"
          inactive-text="否"
          @change="updateSettings"
        />
      </el-form-item>

      <el-form-item label="保存原图">
        <el-switch
          v-model="settings.saveOriginal"
          active-text="是"
          inactive-text="否"
          @change="updateSettings"
        />
      </el-form-item>

      <el-form-item label="创建子文件夹">
        <el-switch
          v-model="settings.createSubfolder"
          active-text="是"
          inactive-text="否"
          @change="updateSettings"
        />
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { mapState } from 'vuex'
import { Cpu } from '@element-plus/icons-vue'

export default {
  name: 'PerformanceSettings',
  components: {
    Cpu
  },
  data() {
    return {
      settings: {
        maxConcurrent: 3,
        delay: 1000,
        retryCount: 3,
        timeout: 30,
        skipExisting: true,
        saveOriginal: true,
        createSubfolder: true
      }
    }
  },
  computed: {
    ...mapState(['downloadSettings'])
  },
  methods: {
    formatDelay(value) {
      if (value < 1000) {
        return `${value}毫秒`
      } else {
        return `${(value / 1000).toFixed(1)}秒`
      }
    },

    updateSettings() {
      // 更新store中的设置
      this.$store.commit('updateDownloadSettings', this.settings)
      // 保存设置
      this.$store.dispatch('saveSettings')
    },

    loadSettings() {
      // 从store加载设置
      if (this.downloadSettings) {
        this.settings = {
          maxConcurrent: this.downloadSettings.maxConcurrent || 3,
          delay: this.downloadSettings.delay || 1000,
          retryCount: this.downloadSettings.retryCount || 3,
          timeout: this.downloadSettings.timeout || 30,
          skipExisting: this.downloadSettings.skipExisting !== false,
          saveOriginal: this.downloadSettings.saveOriginal !== false,
          createSubfolder: this.downloadSettings.createSubfolder !== false
        }
      }
    }
  },

  mounted() {
    // 组件挂载时加载设置
    this.loadSettings()
  },

  watch: {
    downloadSettings: {
      handler() {
        // store中的设置变化时重新加载
        this.loadSettings()
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.performance-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.slider-container {
  width: 100%;
}

.slider-tip {
  margin-top: 5px;
  text-align: center;
}

.timeout-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit {
  color: var(--text-muted);
  font-size: 14px;
}

.el-divider {
  margin: 15px 0;
}
</style>
