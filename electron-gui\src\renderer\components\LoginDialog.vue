<template>
  <el-dialog
    v-model="visible"
    title="Pixiv 登录"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div class="login-dialog">
      <!-- 登录状态显示 -->
      <div class="status-section">
        <div class="status-icon">
          <el-icon v-if="loginStatus === 'checking'" class="rotating">
            <Loading />
          </el-icon>
          <el-icon v-else-if="loginStatus === 'selenium_ready'" color="#409EFF">
            <Monitor />
          </el-icon>
          <el-icon v-else-if="loginStatus === 'success'" color="#67C23A">
            <SuccessFilled />
          </el-icon>
          <el-icon v-else-if="loginStatus === 'failed'" color="#F56C6C">
            <CircleCloseFilled />
          </el-icon>
          <el-icon v-else color="#E6A23C">
            <InfoFilled />
          </el-icon>
        </div>
        
        <div class="status-text">
          <h3>{{ statusTitle }}</h3>
          <p>{{ statusMessage }}</p>
        </div>
      </div>

      <!-- 进度条 -->
      <el-progress
        v-if="showProgress"
        :percentage="progressPercentage"
        :status="progressStatus"
        :stroke-width="8"
        class="progress-bar"
      />

      <!-- 登录步骤指引 -->
      <div v-if="loginStatus === 'selenium_ready'" class="login-steps">
        <el-alert
          title="请在浏览器中完成登录"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ol class="steps-list">
              <li>在打开的浏览器窗口中访问 Pixiv 登录页面</li>
              <li>输入您的用户名和密码完成登录</li>
              <li>确保能正常访问 Pixiv 主页</li>
              <li>返回此处点击"确认登录"按钮</li>
            </ol>
          </template>
        </el-alert>
      </div>

      <!-- 错误信息 -->
      <el-alert
        v-if="errorMessage"
        :title="errorMessage"
        type="error"
        :closable="false"
        show-icon
        class="error-alert"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="loginStatus === 'selenium_ready'"
          type="primary"
          @click="confirmLogin"
          :loading="confirming"
        >
          <el-icon><Check /></el-icon>
          确认登录
        </el-button>
        
        <el-button
          v-if="showCancelButton"
          @click="cancelLogin"
          :disabled="confirming"
        >
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        
        <el-button
          v-if="loginStatus === 'success'"
          type="primary"
          @click="closeDialog"
        >
          <el-icon><Check /></el-icon>
          完成
        </el-button>
        
        <el-button
          v-if="loginStatus === 'failed'"
          type="primary"
          @click="retryLogin"
        >
          <el-icon><Refresh /></el-icon>
          重试
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { 
  Loading, 
  Monitor, 
  SuccessFilled, 
  CircleCloseFilled, 
  InfoFilled,
  Check,
  Close,
  Refresh
} from '@element-plus/icons-vue'

export default {
  name: 'LoginDialog',
  inject: ['$api'],
  components: {
    Loading,
    Monitor,
    SuccessFilled,
    CircleCloseFilled,
    InfoFilled,
    Check,
    Close,
    Refresh
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'login-success', 'login-failed', 'login-cancelled'],
  data() {
    return {
      loginStatus: 'starting', // starting, checking, selenium_ready, success, failed
      statusMessage: '正在检查登录状态...',
      errorMessage: '',
      confirming: false,
      progressPercentage: 0,
      forceRelogin: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    statusTitle() {
      const titles = {
        starting: '正在启动登录流程',
        checking: '检查现有登录状态',
        selenium_starting: '正在启动浏览器',
        selenium_ready: '等待手动登录',
        confirming: '正在验证登录状态',
        success: '登录成功',
        failed: '登录失败'
      }
      return titles[this.loginStatus] || '登录中'
    },
    showProgress() {
      return ['starting', 'checking', 'selenium_starting', 'confirming'].includes(this.loginStatus)
    },
    progressStatus() {
      if (this.loginStatus === 'success') return 'success'
      if (this.loginStatus === 'failed') return 'exception'
      return null
    },
    showCancelButton() {
      return ['starting', 'checking', 'selenium_starting', 'selenium_ready'].includes(this.loginStatus)
    }
  },
  watch: {
    modelValue(newVal) {
      if (newVal) {
        this.startLogin()
      } else {
        this.resetDialog()
      }
    }
  },
  methods: {
    async startLogin(forceRelogin = false) {
      this.forceRelogin = forceRelogin
      this.resetDialog()
      this.loginStatus = 'starting'
      this.progressPercentage = 10
      
      try {
        // 调用后端登录API
        const response = await this.$api.login(forceRelogin)
        
        if (response.success) {
          if (response.method === 'cookies') {
            // 使用cookies登录成功
            this.loginStatus = 'success'
            this.statusMessage = '使用现有登录信息登录成功'
            this.progressPercentage = 100
            this.$emit('login-success', response)
          } else if (response.method === 'selenium') {
            // 需要手动登录
            this.loginStatus = 'selenium_ready'
            this.statusMessage = response.message
            this.progressPercentage = 50
          }
        } else {
          this.loginStatus = 'failed'
          this.errorMessage = response.message || '登录启动失败'
          this.progressPercentage = 0
        }
      } catch (error) {
        console.error('登录失败:', error)
        this.loginStatus = 'failed'
        this.errorMessage = error.message || '网络连接失败'
        this.progressPercentage = 0
      }
    },
    
    async confirmLogin() {
      this.confirming = true
      this.loginStatus = 'confirming'
      this.statusMessage = '正在验证登录状态...'
      this.progressPercentage = 80
      
      try {
        const response = await this.$api.confirmLogin()
        
        if (response.success) {
          this.loginStatus = 'success'
          this.statusMessage = `登录成功！已保存 ${response.cookies_count || 0} 个认证信息`
          this.progressPercentage = 100
          this.$emit('login-success', response)
        } else {
          this.loginStatus = 'failed'
          this.errorMessage = response.message || '登录验证失败'
          this.progressPercentage = 50
          this.$emit('login-failed', response)
        }
      } catch (error) {
        console.error('确认登录失败:', error)
        this.loginStatus = 'failed'
        this.errorMessage = error.message || '验证过程出错'
        this.progressPercentage = 50
        this.$emit('login-failed', { error: error.message })
      } finally {
        this.confirming = false
      }
    },
    
    async cancelLogin() {
      try {
        await this.$api.cancelLogin()
        this.$emit('login-cancelled')
        this.visible = false
      } catch (error) {
        console.error('取消登录失败:', error)
        this.visible = false
      }
    },
    
    retryLogin() {
      this.startLogin(true) // 强制重新登录
    },
    
    closeDialog() {
      this.visible = false
    },
    
    resetDialog() {
      this.loginStatus = 'starting'
      this.statusMessage = '正在检查登录状态...'
      this.errorMessage = ''
      this.confirming = false
      this.progressPercentage = 0
    }
  }
}
</script>

<style scoped>
.login-dialog {
  padding: 20px 0;
}

.status-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.status-icon {
  font-size: 48px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-text h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.status-text p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.progress-bar {
  margin: 20px 0;
}

.login-steps {
  margin: 20px 0;
}

.steps-list {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.steps-list li {
  margin: 8px 0;
  line-height: 1.5;
}

.error-alert {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
