# 下载设置组件使用说明

## 功能概述

下载设置组件 (`DownloadSettings.vue`) 提供了完整的Pixiv作品下载配置功能，支持四种下载模式：

1. **关注画师作品** - 下载关注的画师的最新作品
2. **搜索作品** - 根据关键词搜索并下载作品
3. **排行榜作品** - 下载各类排行榜作品
4. **画师作品** - 下载指定画师的作品

## 各模式详细说明

### 1. 关注画师作品模式

**设置项：**
- 下载方式：按页码 / 按天数
- 获取天数：1-365天（仅按天数模式）
- 页码范围：起始页-结束页（仅按页码模式）
- 下载路径：作品保存位置

**URL生成逻辑：**
```
https://www.pixiv.net/bookmark_new_illust.php?p=页码数
```

### 2. 搜索作品模式

**设置项：**
- 搜索关键词：要搜索的内容
- 搜索范围：插画·漫画 / 插画 / 漫画
- 收藏值：最低收藏数筛选
- 搜索模式：全部 / 全年龄 / R-18
- 页码范围：起始页-结束页
- 下载路径：作品保存位置

**URL生成逻辑：**
- 插画·漫画：`https://www.pixiv.net/tags/关键词+收藏值users入り/artworks`
- 插画：`https://www.pixiv.net/tags/关键词+收藏值users入り/illustrations`
- 漫画：`https://www.pixiv.net/tags/关键词+收藏值users入り/manga`

### 3. 排行榜作品模式

**设置项：**
- 排行榜类型：综合 / 插画 / 动图 / 漫画
- 排行榜选择：今日 / 本周 / AI生成（仅综合类型）
- 排行榜模式：全年龄 / R-18
- 排行榜日期：选择具体日期
- 下载路径：作品保存位置

**URL生成逻辑：**
```
https://www.pixiv.net/ranking.php?mode=排行榜选择+排行榜模式&content=内容类型&date=日期
```

**键值对映射：**
- 今日：daily
- 本周：weekly
- AI生成：_ai
- 全年龄：（空值）
- R-18：_r18

### 4. 画师作品模式

**设置项：**
- 画师ID：目标画师的用户ID
- 页码范围：起始页-结束页
- 下载路径：作品保存位置

**URL生成逻辑：**
```
https://www.pixiv.net/users/画师ID/artworks?p=页码数
```

## 组件API

### Props
无

### Events
无

### Methods

#### `generateUrl()`
根据当前设置生成对应的Pixiv URL

#### `startDownload()`
开始下载任务

#### `stopDownload()`
停止当前下载任务

#### `previewUrl()`
预览生成的URL

#### `selectPath(mode)`
选择下载路径
- `mode`: 'following' | 'search' | 'ranking' | 'artist'

### Store集成

组件与Vuex store集成，自动保存和加载设置：

```javascript
// Store结构
downloadSettings: {
  mode: 'following',
  following: { ... },
  search: { ... },
  ranking: { ... },
  artist: { ... }
}
```

## 使用示例

```vue
<template>
  <DownloadSettings />
</template>

<script>
import DownloadSettings from '@/components/DownloadSettings.vue'

export default {
  components: {
    DownloadSettings
  }
}
</script>
```

## 注意事项

1. **路径选择**：所有模式都需要设置下载路径才能开始下载
2. **必填字段**：
   - 搜索模式：需要填写搜索关键词
   - 画师模式：需要填写画师ID
3. **AI生成选项**：只有综合排行榜类型才支持AI生成选项
4. **设置持久化**：所有设置会自动保存到本地存储
5. **URL预览**：可以使用"预览URL"功能查看生成的链接

## 后端集成

组件通过API服务与后端通信：

```javascript
// 开始下载
await this.$api.startDownload({
  mode: this.downloadMode,
  url: generatedUrl,
  settings: currentSettings
})

// 停止下载
await this.$api.stopDownload()
```

后端需要实现对应的IPC处理器来处理下载请求。
