/**
 * API服务
 * 统一管理与后端的IPC通信（精简版）
 */

class ApiService {
  constructor() {
    // 检查electronAPI是否可用
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }
    this.electronAPI = window.electronAPI
  }

  // === 健康检查 ===

  /**
   * 健康检查
   */
  async healthCheck() {
    return this.electronAPI.healthCheck()
  }

  /**
   * 检查后端连接状态
   */
  async checkConnection() {
    try {
      await this.healthCheck()
      return true
    } catch (error) {
      console.error('❌ 后端连接检查失败:', error)
      return false
    }
  }

  // === 认证相关API ===

  /**
   * 获取登录状态
   */
  async getAuthStatus() {
    return this.electronAPI.getAuthStatus()
  }

  /**
   * 登录
   */
  async login(forceRelogin = false) {
    return this.electronAPI.login(forceRelogin)
  }

  /**
   * 检查cookies状态
   */
  async checkCookies() {
    return this.electronAPI.checkCookies()
  }

  /**
   * 启动Selenium登录
   */
  async startSeleniumLogin() {
    return this.electronAPI.startSeleniumLogin()
  }

  /**
   * 确认登录完成
   */
  async confirmLogin() {
    return this.electronAPI.confirmLogin()
  }

  /**
   * 取消登录
   */
  async cancelLogin() {
    return this.electronAPI.cancelLogin()
  }

  // === 下载相关API ===

  /**
   * 开始下载
   */
  async startDownload(settings) {
    return this.electronAPI.startDownload(settings)
  }

  /**
   * 停止下载
   */
  async stopDownload() {
    return this.electronAPI.stopDownload()
  }

  /**
   * 暂停下载
   */
  async pauseDownload() {
    return this.electronAPI.pauseDownload()
  }

  /**
   * 继续下载
   */
  async resumeDownload() {
    return this.electronAPI.resumeDownload()
  }

  /**
   * 获取下载状态
   */
  async getDownloadStatus() {
    return this.electronAPI.getDownloadStatus()
  }

  // === 配置相关API ===

  /**
   * 获取配置
   */
  async getConfig() {
    return this.electronAPI.getConfig()
  }

  /**
   * 更新配置
   */
  async updateConfig(config) {
    return this.electronAPI.updateConfig(config)
  }


}

// 创建单例实例
const apiService = new ApiService()

export default apiService
