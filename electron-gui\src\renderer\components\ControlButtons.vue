<template>
  <el-card class="control-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <el-icon><Operation /></el-icon>
        <span>下载控制</span>
      </div>
    </template>
    
    <div class="control-buttons">
      <el-button
        type="primary"
        size="large"
        @click="startDownload"
        :disabled="!canStart"
        :loading="isStarting"
        class="control-btn start-btn"
      >
        <el-icon><VideoPlay /></el-icon>
        <span>开始下载</span>
      </el-button>

      <el-button
        type="danger"
        size="large"
        @click="stopDownload"
        :disabled="!isDownloading"
        :loading="isStopping"
        class="control-btn stop-btn"
      >
        <el-icon><VideoPause /></el-icon>
        <span>停止下载</span>
      </el-button>
      
      <el-button
        type="warning"
        size="large"
        @click="pauseDownload"
        :disabled="!isDownloading || isPaused"
        class="control-btn pause-btn"
      >
        <el-icon>
          <VideoPause v-if="!isPaused" />
          <VideoPlay v-else />
        </el-icon>
        <span>{{ isPaused ? '继续' : '暂停' }}</span>
      </el-button>
      
      <el-button
        type="info"
        size="large"
        @click="clearLogs"
        class="control-btn clear-btn"
      >
        <el-icon><Delete /></el-icon>
        <span>清空日志</span>
      </el-button>
    </div>
    
    <!-- 下载前检查 -->
    <div v-if="validationErrors.length > 0" class="validation-errors">
      <el-alert
        title="配置检查"
        type="warning"
        :closable="false"
        show-icon
      >
        <ul class="error-list">
          <li v-for="error in validationErrors" :key="error">{{ error }}</li>
        </ul>
      </el-alert>
    </div>
  </el-card>
</template>

<script>
import { mapState } from 'vuex'
import { Operation, VideoPlay, VideoPause, Delete } from '@element-plus/icons-vue'
import unifiedConfigService from '../services/unified-config.js'
import errorHandlerService from '../services/error-handler.js'

export default {
  name: 'ControlButtons',
  inject: ['$api', '$ipc'],
  components: {
    Operation,
    VideoPlay,
    VideoPause,
    Delete
  },
  data() {
    return {
      isStarting: false,
      isStopping: false,
      isPaused: false
    }
  },
  computed: {
    ...mapState(['isDownloading', 'isLoggedIn', 'downloadSettings']),
    
    canStart() {
      return !this.isDownloading && this.isLoggedIn && this.validationErrors.length === 0
    },
    
    validationErrors() {
      if (!this.isLoggedIn) {
        return ['请先登录Pixiv账号']
      }

      const mode = this.downloadSettings?.mode || 'following'
      const currentSettings = this.getCurrentSettings()

      // 使用统一配置服务进行验证
      try {
        const config = unifiedConfigService.createUnifiedConfig(mode, currentSettings)
        return unifiedConfigService.validateConfig(config)
      } catch (error) {
        return [error.message]
      }
    }
  },
  
  methods: {
    getCurrentDownloadPath() {
      const mode = this.downloadSettings?.mode || 'following'

      switch (mode) {
        case 'following':
          return this.downloadSettings?.following?.downloadPath
        case 'search':
          return this.downloadSettings?.search?.downloadPath
        case 'ranking':
          return this.downloadSettings?.ranking?.downloadPath
        case 'artist':
          return this.downloadSettings?.artist?.downloadPath
        default:
          return null
      }
    },

    getCurrentSettings() {
      const mode = this.downloadSettings?.mode || 'following'

      let settings = {}
      switch (mode) {
        case 'following':
          settings = this.downloadSettings?.following || {}
          break
        case 'search':
          settings = this.downloadSettings?.search || {}
          break
        case 'ranking':
          settings = this.downloadSettings?.ranking || {}
          break
        case 'artist':
          settings = this.downloadSettings?.artist || {}
          break
        default:
          settings = {}
      }

      // 确保返回可序列化的对象
      return JSON.parse(JSON.stringify(settings))
    },

    // 使用统一配置服务替换重复的配置转换逻辑
    convertToBackendConfig(mode, settings) {
      return unifiedConfigService.createUnifiedConfig(mode, settings)
    },

    async startDownload() {
      if (!this.canStart) {
        this.$message.warning('请检查配置后再开始下载')
        return
      }

      this.isStarting = true

      try {
        const mode = this.downloadSettings?.mode || 'following'
        const currentSettings = this.getCurrentSettings()

        // 使用统一配置服务创建后端配置
        const downloadConfig = this.convertToBackendConfig(mode, currentSettings)

        console.log('🚀 准备开始下载，配置:', downloadConfig)

        // 验证配置是否可序列化
        try {
          JSON.stringify(downloadConfig)
          console.log('✅ 配置序列化验证通过')
        } catch (e) {
          console.error('❌ 配置序列化失败:', e)
          throw new Error('配置包含不可序列化的对象: ' + e.message)
        }

        // 调用后端API开始下载
        console.log('📡 调用API开始下载...')
        const response = await this.$api.startDownload(downloadConfig)

        if (response.success) {
          this.$store.commit('setDownloading', true)
          this.$store.commit('addLog', '🚀 下载任务已提交到后台')
          this.$message.success('下载任务已在后台启动，请查看日志获取进度')
        } else {
          throw new Error(response.message || '启动下载失败')
        }
      } catch (error) {
        // 使用统一错误处理服务
        const errorInfo = errorHandlerService.handleApiError(error, '下载启动')

        console.error('启动下载失败:', errorInfo.originalError)
        this.$message.error(errorInfo.message)
        this.$store.commit('addLog', `❌ ${errorInfo.message}`)

        // 如果是认证错误，跳转到登录页面
        if (errorHandlerService.isErrorType(error, 'AUTH_ERROR')) {
          this.$router.push('/auth')
        }
      } finally {
        this.isStarting = false
      }
    },
    
    async stopDownload() {
      this.isStopping = true

      try {
        const response = await this.$api.stopDownload()

        if (response.success) {
          this.$store.commit('setDownloading', false)
          this.isPaused = false
          this.$store.commit('addLog', '⏹️ 下载任务已停止')
          this.$message.success('下载任务已停止')
        } else {
          throw new Error(response.message || '停止下载失败')
        }
      } catch (error) {
        const errorInfo = errorHandlerService.handleApiError(error, '停止下载')
        console.error('停止下载失败:', errorInfo.originalError)
        this.$message.error(errorInfo.message)
      } finally {
        this.isStopping = false
      }
    },
    
    async pauseDownload() {
      try {
        const action = this.isPaused ? 'resume' : 'pause'
        // 使用IPC通信替代HTTP API调用
        const response = await this.$ipc.sendMessage(`download/${action}`, {})

        if (response.success) {
          this.isPaused = !this.isPaused
          const message = this.isPaused ? '⏸️ 下载任务已暂停' : '▶️ 下载任务已继续'
          this.$store.commit('addLog', message)
          this.$message.success(this.isPaused ? '下载已暂停' : '下载已继续')
        } else {
          throw new Error(response.message || '操作失败')
        }
      } catch (error) {
        this.$message.error('操作失败: ' + error.message)
      }
    },

    clearLogs() {
      this.$store.commit('clearLogs')
      this.$message.success('日志已清空')
    }
  }
}
</script>

<style scoped>
.control-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.control-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  align-items: center;
  justify-items: center;
}

.control-btn {
  height: 50px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  min-width: 130px !important;
  max-width: 130px !important;
  width: 130px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 12px 16px !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  align-self: center !important;
  justify-self: center !important;
}

.control-btn .el-icon {
  margin-right: 8px !important;
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

.control-btn span {
  flex: 1 !important;
  text-align: center !important;
  white-space: nowrap !important;
}

/* 确保所有按钮类型都有相同尺寸 */
.start-btn,
.stop-btn,
.pause-btn,
.clear-btn {
  height: 50px !important;
  width: 130px !important;
  min-width: 130px !important;
  max-width: 130px !important;
}

.validation-errors {
  margin-top: 15px;
}

.error-list {
  margin: 0;
  padding-left: 20px;
}

.error-list li {
  margin: 5px 0;
  color: var(--warning-color);
}
</style>
