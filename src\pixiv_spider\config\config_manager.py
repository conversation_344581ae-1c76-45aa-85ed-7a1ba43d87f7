"""
配置管理器

负责加载、保存和管理应用程序配置
"""

import json
import pickle
import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

from ..models.config import DownloadConfig, SpiderConfig
from ..models.exceptions import ConfigError
from .settings import (
    DEFAULT_CONFIG_FILE, DEFAULT_CACHE_FILE,
    DEFAULT_DOWNLOAD_PATH, DEFAULT_LOG_PATH, DEFAULT_COOKIES_PATH
)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为当前目录
        """
        self.config_dir = Path(config_dir) if config_dir else Path.cwd()
        self.config_file = self.config_dir / DEFAULT_CONFIG_FILE
        
        # 创建专门的cookie目录
        self.cookie_dir = self.config_dir / DEFAULT_COOKIES_PATH
        self.cookies_file = self.cookie_dir / "pixiv_cookies.pkl"
        
        self.cache_file = self.config_dir / DEFAULT_CACHE_FILE
        
        # 确保配置目录和cookie目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.cookie_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化配置
        self._download_config: Optional[DownloadConfig] = None
        self._spider_config: Optional[SpiderConfig] = None
        
        logger = logging.getLogger(__name__)
        self.logger = logger

    def _load_config_file(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}

    def _save_config_file(self, data: Dict[str, Any]) -> None:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            raise ConfigError(f"保存配置文件失败: {e}")
    
    def load_download_config(self, force_reload: bool = False) -> DownloadConfig:
        """加载下载配置"""
        if self._download_config is None or force_reload:
            try:
                data = self._load_config_file()
                download_data = data.get('download', {})
                self._download_config = DownloadConfig.from_dict(download_data)

                # 如果是新配置，保存默认值
                if not data:
                    self.save_download_config(self._download_config)

                if force_reload:
                    self.logger.info("强制重新加载下载配置")
            except Exception as e:
                self.logger.error(f"加载下载配置失败: {e}")
                self._download_config = DownloadConfig()

        return self._download_config

    def reload_config(self) -> None:
        """强制重新加载所有配置"""
        self.logger.info("强制重新加载所有配置...")
        self._download_config = None
        self._spider_config = None
        self.load_download_config(force_reload=True)
        self.load_spider_config(force_reload=True)
        self.logger.info("配置重新加载完成")
    
    def save_download_config(self, config: DownloadConfig) -> None:
        """保存下载配置"""
        try:
            # 读取现有配置
            data = self._load_config_file()

            # 更新下载配置
            data['download'] = config.to_dict()

            # 保存配置
            self._save_config_file(data)

            self._download_config = config
            self.logger.info("下载配置已保存")
        except Exception as e:
            self.logger.error(f"保存下载配置失败: {e}")
            raise ConfigError(f"保存下载配置失败: {e}")
    
    def load_spider_config(self, force_reload: bool = False) -> SpiderConfig:
        """加载爬虫配置"""
        if self._spider_config is None or force_reload:
            try:
                data = self._load_config_file()
                spider_data = data.get('spider', {})
                self._spider_config = SpiderConfig.from_dict(spider_data)

                # 如果是新配置，保存默认值
                if not data:
                    self.save_spider_config(self._spider_config)

                if force_reload:
                    self.logger.info("强制重新加载爬虫配置")
            except Exception as e:
                self.logger.error(f"加载爬虫配置失败: {e}")
                self._spider_config = SpiderConfig()

        return self._spider_config
    
    def save_spider_config(self, config: SpiderConfig) -> None:
        """保存爬虫配置"""
        try:
            # 读取现有配置
            data = self._load_config_file()

            # 更新爬虫配置
            data['spider'] = config.to_dict()

            # 保存配置
            self._save_config_file(data)

            self._spider_config = config
            self.logger.info("爬虫配置已保存")
        except Exception as e:
            self.logger.error(f"保存爬虫配置失败: {e}")
            raise ConfigError(f"保存爬虫配置失败: {e}")
    
    def load_cookies(self) -> Optional[Dict[str, Any]]:
        """加载Cookie"""
        try:
            if self.cookies_file.exists():
                with open(self.cookies_file, 'rb') as f:
                    cookies = pickle.load(f)
                self.logger.info("Cookie已加载")
                return cookies
        except Exception as e:
            self.logger.error(f"加载Cookie失败: {e}")
        
        return None
    
    def save_cookies(self, cookies: Dict[str, Any]) -> None:
        """保存Cookie"""
        try:
            with open(self.cookies_file, 'wb') as f:
                pickle.dump(cookies, f)
            self.logger.info("Cookie已保存")
        except Exception as e:
            self.logger.error(f"保存Cookie失败: {e}")
            raise ConfigError(f"保存Cookie失败: {e}")
    
    def load_cache(self) -> Optional[Dict[str, Any]]:
        """加载缓存"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'rb') as f:
                    cache = pickle.load(f)
                self.logger.info("缓存已加载")
                return cache
        except Exception as e:
            self.logger.error(f"加载缓存失败: {e}")
        
        return None
    
    def save_cache(self, cache: Dict[str, Any]) -> None:
        """保存缓存"""
        try:
            with open(self.cache_file, 'wb') as f:
                pickle.dump(cache, f)
            self.logger.debug("缓存已保存")
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def clear_cache(self) -> None:
        """清除缓存"""
        try:
            if self.cache_file.exists():
                self.cache_file.unlink()
                self.logger.info("缓存已清除")
        except Exception as e:
            self.logger.error(f"清除缓存失败: {e}")
    
    def clear_cookies(self) -> None:
        """清除Cookie"""
        try:
            if self.cookies_file.exists():
                self.cookies_file.unlink()
                self.logger.info("Cookie已清除")
        except Exception as e:
            self.logger.error(f"清除Cookie失败: {e}")
    
    def ensure_directories(self) -> None:
        """确保必要的目录存在"""
        try:
            # 获取配置
            download_config = self.load_download_config()
            
            # 创建下载目录
            download_path = Path(download_config.save_path)
            download_path.mkdir(parents=True, exist_ok=True)
            
            # 创建日志目录
            log_path = Path(DEFAULT_LOG_PATH)
            log_path.mkdir(parents=True, exist_ok=True)
            
            # 创建cookie目录
            self.cookie_dir.mkdir(parents=True, exist_ok=True)
            
            self.logger.info("目录检查完成")
        except Exception as e:
            self.logger.error(f"创建目录失败: {e}")
            raise ConfigError(f"创建目录失败: {e}")
    
    def validate_config(self) -> bool:
        """验证配置"""
        try:
            download_config = self.load_download_config()
            spider_config = self.load_spider_config()
            
            # 验证下载配置
            download_errors = download_config.validate()
            if download_errors:
                self.logger.error(f"下载配置验证失败: {download_errors}")
                return False
            
            # 验证目录权限
            download_path = Path(download_config.save_path)
            if not os.access(download_path.parent, os.W_OK):
                self.logger.error(f"下载目录无写入权限: {download_path}")
                return False
            
            self.logger.info("配置验证通过")
            return True
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def export_config(self, file_path: str) -> None:
        """导出配置到文件"""
        try:
            config_data = {
                'download': self.load_download_config().to_dict(),
                'spider': self.load_spider_config().to_dict(),
                'version': '3.0.0',
                'exported_at': json.dumps(None)  # 时间戳会在实际实现中添加
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已导出到: {file_path}")
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            raise ConfigError(f"导出配置失败: {e}")
    
    def import_config(self, file_path: str) -> None:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 导入下载配置
            if 'download' in config_data:
                download_config = DownloadConfig.from_dict(config_data['download'])
                self.save_download_config(download_config)
            
            # 导入爬虫配置
            if 'spider' in config_data:
                spider_config = SpiderConfig.from_dict(config_data['spider'])
                self.save_spider_config(spider_config)
            
            self.logger.info(f"配置已从 {file_path} 导入")
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            raise ConfigError(f"导入配置失败: {e}")
    
    def reset_to_defaults(self) -> None:
        """重置为默认配置"""
        try:
            self._download_config = DownloadConfig()
            self._spider_config = SpiderConfig()
            
            self.save_download_config(self._download_config)
            self.save_spider_config(self._spider_config)
            
            self.logger.info("配置已重置为默认值")
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
            raise ConfigError(f"重置配置失败: {e}") 
