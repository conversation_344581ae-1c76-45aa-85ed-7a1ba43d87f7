"""
下载服务

负责文件下载、作品处理和下载状态检查
"""

import os
import re
import glob
import zipfile
import logging
from pathlib import Path
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor, as_completed, TimeoutError
from typing import List, Dict, Tuple, Optional, Callable
import time

from ..interfaces.download_interface import IDownloadService
from ..models.artwork import Artwork, ArtworkPage, ArtworkType, ArtworkStatus
from ..models.config import DownloadConfig, ClassifyMode, GifMode
from ..models.exceptions import DownloadError, NetworkError
from ..config.config_manager import ConfigManager

try:
    from PIL import Image
except ImportError:
    Image = None


class DownloadService(IDownloadService):
    """下载服务类"""
    
    def __init__(self, api_service, config_manager: Optional[ConfigManager] = None):
        """
        初始化下载服务
        
        Args:
            api_service: API服务实例
            config_manager: 配置管理器
        """
        self.api_service = api_service
        self.config_manager = config_manager or ConfigManager()
        self.download_config = self.config_manager.load_download_config()
        self.spider_config = self.config_manager.load_spider_config()
        self.logger = logging.getLogger(__name__)
        
        # 线程池 - 延迟初始化以提高启动速度
        self.executor = None
        self._max_workers = self.spider_config.concurrent_downloads

        # 下载缓存 - 延迟扫描以提高启动速度
        self._downloaded_cache: Dict[str, str] = {}
        self._cache_initialized = False

        # 回调函数
        self._progress_callback: Optional[Callable] = None
        self._status_callback: Optional[Callable] = None

        # 停止标志
        self._should_stop = False

    def _ensure_executor_initialized(self) -> None:
        """确保线程池已初始化"""
        if self.executor is None:
            self.logger.info(f"🔧 初始化线程池，工作线程数: {self._max_workers}")
            self.executor = ThreadPoolExecutor(max_workers=self._max_workers)

    def _ensure_cache_initialized(self) -> None:
        """确保缓存已初始化"""
        if not self._cache_initialized:
            self.logger.info("🔍 初始化下载缓存...")
            start_time = time.time()
            self._scan_downloaded_artworks()
            self._cache_initialized = True
            init_time = time.time() - start_time
            self.logger.info(f"✅ 缓存初始化完成 (耗时: {init_time:.2f}秒)")

    def set_progress_callback(self, callback: Callable) -> None:
        """设置进度回调函数"""
        self._progress_callback = callback
    
    def set_status_callback(self, callback: Callable) -> None:
        """设置状态回调函数"""
        self._status_callback = callback
    
    def stop_download(self) -> None:
        """立即停止下载，不等待任务完成"""
        self.logger.info("🛑 下载服务收到停止信号，立即停止下载...")

        # 设置停止标志
        self._should_stop = True

        # 立即关闭线程池，不等待任务完成
        if hasattr(self, 'executor') and self.executor:
            try:
                # 获取当前活跃任务数
                active_count = 0
                if hasattr(self.executor, '_threads'):
                    active_count = len([t for t in self.executor._threads if t.is_alive()])

                if active_count > 0:
                    self.logger.info(f"⏳ 通知 {active_count} 个下载任务停止...")

                    # 给任务很短的时间来检查停止标志
                    import time
                    for i in range(3):  # 最多等待0.3秒
                        if hasattr(self.executor, '_threads'):
                            current_active = len([t for t in self.executor._threads if t.is_alive()])
                            if current_active == 0:
                                break
                        time.sleep(0.1)

                    # 不再等待，直接进入清理阶段
                    if hasattr(self.executor, '_threads'):
                        remaining_active = len([t for t in self.executor._threads if t.is_alive()])
                        if remaining_active > 0:
                            self.logger.info(f"🔄 将强制停止 {remaining_active} 个任务")
                        else:
                            self.logger.info("✅ 所有下载任务已停止")
                else:
                    self.logger.info("ℹ️ 当前没有活跃的下载任务")

            except Exception as e:
                self.logger.error(f"❌ 检查下载任务状态失败: {e}")

        self.logger.info("🛑 下载停止信号已发送")
    
    def cleanup_resources(self) -> None:
        """清理下载服务资源"""
        try:
            self.logger.info("🧹 开始清理下载服务资源...")

            # 停止所有下载任务
            self._should_stop = True
            self.logger.info("🛑 已设置停止标志")

            # 关闭线程池
            if hasattr(self, 'executor') and self.executor:
                self.logger.info("🔄 正在关闭下载线程池...")

                # 获取当前活跃任务数
                try:
                    active_count = self.executor._threads.__len__() if hasattr(self.executor, '_threads') else 0
                    self.logger.info(f"📊 当前活跃线程数: {active_count}")
                except Exception:
                    pass

                # 立即强制关闭，不等待任务完成
                try:
                    # 直接使用wait=False进行强制关闭
                    self.executor.shutdown(wait=False)
                    self.logger.info("🔄 线程池已强制关闭（不等待任务完成）")

                    # 记录仍在运行的线程数量
                    if hasattr(self.executor, '_threads'):
                        running_threads = [t for t in self.executor._threads if t.is_alive()]
                        if running_threads:
                            self.logger.info(f"📊 {len(running_threads)} 个线程将在后台自然结束")

                except Exception as e:
                    self.logger.warning(f"⚠️ 强制关闭线程池失败: {e}")
                    # 即使失败也继续清理其他资源

                self.executor = None
                self.logger.info("🗑️ 线程池引用已清除")

            # 清空下载缓存
            if hasattr(self, '_downloaded_cache'):
                cache_size = len(self._downloaded_cache)
                self._downloaded_cache.clear()
                self.logger.info(f"📦 下载缓存已清空，释放了 {cache_size} 个缓存项")

            # 清理临时文件
            try:
                import tempfile
                import glob
                temp_pattern = os.path.join(tempfile.gettempdir(), "pixiv_download_*")
                temp_files = glob.glob(temp_pattern)
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                        self.logger.debug(f"🗑️ 清理临时文件: {temp_file}")
                    except Exception:
                        pass
                if temp_files:
                    self.logger.info(f"🧹 清理了 {len(temp_files)} 个临时文件")
            except Exception as e:
                self.logger.debug(f"清理临时文件失败: {e}")

            # 重置回调函数
            self._progress_callback = None
            self._status_callback = None
            self.logger.info("🔗 回调函数已重置")

            # 重置停止标志
            self._should_stop = False

            self.logger.info("✅ 下载服务资源清理完成")

        except Exception as e:
            self.logger.error(f"❌ 清理下载服务资源时出错: {e}")
            import traceback
            self.logger.debug(f"清理异常堆栈: {traceback.format_exc()}")
    
    def reset_state(self) -> None:
        """重置下载服务状态"""
        try:
            # 重置停止标志
            self._should_stop = False
            
            # 重新创建线程池
            if not hasattr(self, 'executor') or self.executor is None or self.executor._shutdown:
                self.executor = ThreadPoolExecutor(max_workers=self.spider_config.concurrent_downloads)
                self.logger.info("线程池已重新创建")
            
        except Exception as e:
            self.logger.error(f"重置下载服务状态时出错: {e}")
    
    def _scan_downloaded_artworks(self) -> None:
        """扫描已下载的作品 - 优化版本"""
        if not os.path.exists(self.download_config.save_path):
            self.logger.debug(f"📁 保存路径不存在: {self.download_config.save_path}")
            return

        downloaded_count = 0
        processed_folders = set()

        try:
            # 使用os.walk进行单次遍历，比多次glob更高效
            for root, dirs, files in os.walk(self.download_config.save_path):
                # 限制遍历深度为2层，避免过深遍历
                level = root.replace(self.download_config.save_path, '').count(os.sep)
                if level >= 2:
                    dirs[:] = []  # 不再深入子目录
                    continue

                for dir_name in dirs:
                    folder_path = os.path.join(root, dir_name)

                    if folder_path in processed_folders:
                        continue
                    processed_folders.add(folder_path)

                    # 快速提取作品ID
                    artwork_id = self._extract_artwork_id(dir_name)
                    if artwork_id:
                        # 快速检查是否有图片文件（不深入检查内容）
                        if self._has_images(folder_path, fast_mode=True):
                            self._downloaded_cache[artwork_id] = folder_path
                            downloaded_count += 1

        except Exception as e:
            self.logger.error(f"❌ 扫描已下载作品时出错: {e}")

        self.logger.debug(f"📊 发现 {downloaded_count} 个已下载作品")

    def _extract_artwork_id(self, folder_name: str) -> Optional[str]:
        """快速提取作品ID"""
        # 格式1: title_author_123456 (新格式)
        match = re.search(r'_([^_]+)_(\d+)$', folder_name)
        if match:
            return match.group(2)

        # 格式2: title_123456 (旧格式)
        match = re.search(r'_(\d+)$', folder_name)
        if match:
            return match.group(1)

        return None

    def _has_images(self, folder_path: str, fast_mode: bool = True) -> bool:
        """
        检查文件夹是否有图片文件

        Args:
            folder_path: 文件夹路径
            fast_mode: 快速模式，只检查前几个文件
        """
        try:
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
            count = 0

            for file in os.listdir(folder_path):
                if fast_mode and count >= 3:  # 快速模式只检查前3个文件
                    break

                if any(file.lower().endswith(ext) for ext in image_extensions):
                    return True

                if fast_mode:
                    count += 1

            return False
        except Exception:
            return False

    def refresh_downloaded_cache(self) -> None:
        """刷新已下载作品缓存"""
        self.logger.info("🔄 刷新已下载作品缓存...")
        self._downloaded_cache.clear()
        self._scan_downloaded_artworks()

    def is_downloaded(self, artwork_id: str) -> bool:
        """检查作品是否已下载"""
        return artwork_id in self._downloaded_cache
    
    def get_downloaded_path(self, artwork_id: str) -> Optional[str]:
        """获取已下载作品的路径"""
        return self._downloaded_cache.get(artwork_id)

    def _sanitize_filename(self, filename: str, max_length: int = 50) -> str:
        """
        清理文件名，移除非法字符

        Args:
            filename: 原始文件名
            max_length: 最大长度

        Returns:
            str: 清理后的文件名
        """
        # 移除非法字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        return safe_name[:max_length] if safe_name else 'untitled'
    
    def generate_save_path(self, artwork: Artwork) -> str:
        """
        生成保存路径
        
        Args:
            artwork: 作品对象
            
        Returns:
            str: 保存路径
        """
        # 清理文件名
        safe_title = self._sanitize_filename(artwork.title, 50)
        safe_author = self._sanitize_filename(artwork.author_name, 30)
        folder_name = f"{safe_title}_{safe_author}_{artwork.id}"

        base_path = Path(self.download_config.save_path)

        if self.download_config.classify_mode == ClassifyMode.BY_DATE:
            # 按日期分类
            date_str = artwork.upload_date.strftime('%Y-%m-%d') if artwork.upload_date else 'unknown'
            save_dir = base_path / date_str / folder_name
        elif self.download_config.classify_mode == ClassifyMode.BY_AUTHOR:
            # 按作者分类
            save_dir = base_path / safe_author / folder_name
        elif self.download_config.classify_mode == ClassifyMode.BY_TYPE:
            # 按类型分类
            type_folder = "illustrations" if artwork.type == ArtworkType.ILLUSTRATION else "manga"
            save_dir = base_path / type_folder / folder_name
        else:
            # 平铺结构
            save_dir = base_path / folder_name
        
        return str(save_dir)
    
    def check_artwork_downloaded(self, artwork: Artwork) -> Tuple[bool, str]:
        """
        检查作品是否已下载
        
        Args:
            artwork: 作品对象
            
        Returns:
            Tuple[bool, str]: (是否已下载, 保存路径)
        """
        artwork_id = str(artwork.id)
        
        # 首先检查缓存
        if self.is_downloaded(artwork_id):
            existing_path = self.get_downloaded_path(artwork_id)
            return True, existing_path
        
        # 生成预期的保存路径
        expected_path = self.generate_save_path(artwork)
        
        # 检查预期路径是否存在
        if os.path.exists(expected_path):
            # 检查是否有实际的文件内容
            if self._has_images(expected_path, fast_mode=False):
                # 更新缓存
                self._downloaded_cache[artwork_id] = expected_path
                return True, expected_path
        
        return False, expected_path

    def download_artwork(self, artwork: Artwork) -> bool:
        """
        下载单个作品

        Args:
            artwork: 作品对象

        Returns:
            bool: 是否下载成功
        """
        try:
            # 检查停止信号
            if self._should_stop:
                artwork.status = ArtworkStatus.FAILED
                return False

            # 如果已经标记为跳过，直接返回
            if artwork.status == ArtworkStatus.SKIPPED:
                return True
            
            # 生成保存路径
            save_path = self.generate_save_path(artwork)
            
            # 创建目录
            os.makedirs(save_path, exist_ok=True)
            
            # 更新状态
            artwork.status = ArtworkStatus.DOWNLOADING
            artwork.local_path = save_path
            
            # 获取作品页面信息
            pages_data = self.api_service.get_artwork_pages(artwork.id)
            if not pages_data or 'body' not in pages_data:
                raise DownloadError(f"无法获取作品 {artwork.id} 的页面信息")
            
            pages_info = pages_data['body']
            
            # 下载所有页面
            success_count = 0
            for i, page_info in enumerate(pages_info):
                # 检查停止信号
                if self._should_stop:
                    self.logger.info(f"收到停止信号，停止下载作品 {artwork.id} 的剩余页面")
                    break
                
                page = ArtworkPage(
                    url_original=page_info['urls']['original'],
                    url_medium=page_info['urls']['regular'],
                    url_square_medium=page_info['urls']['small'],
                    width=page_info['width'],
                    height=page_info['height'],
                    page_number=i
                )
                artwork.add_page(page)
                
                # 下载页面
                if self._download_page(artwork, page, save_path):
                    success_count += 1
            
            # 处理动图
            if artwork.type == ArtworkType.UGOIRA:
                self._process_ugoira(artwork, save_path)
            
            # 创建信息文件
            if self.download_config.create_info_file:
                self._create_info_file(artwork, save_path)
            
            # 更新状态
            if success_count > 0:
                artwork.status = ArtworkStatus.COMPLETED
                self._downloaded_cache[str(artwork.id)] = save_path
                self.logger.info(f"作品 {artwork.id} 下载完成，共 {success_count} 个文件")
                return True
            else:
                artwork.status = ArtworkStatus.FAILED
                artwork.error_message = "没有成功下载任何文件"
                return False
                
        except Exception as e:
            self.logger.error(f"下载作品 {artwork.id} 失败: {e}")
            artwork.status = ArtworkStatus.FAILED
            artwork.error_message = str(e)
            return False
    
    def _download_page(self, artwork: Artwork, page: ArtworkPage, save_dir: str) -> bool:
        """下载单个页面"""
        try:
            # 生成文件名
            extension = page.url_original.split('.')[-1]
            if artwork.is_multi_page:
                filename = f"p{page.page_number}.{extension}"
            else:
                filename = f"p0.{extension}"
            
            file_path = os.path.join(save_dir, filename)
            
            # 如果文件已存在，跳过
            if os.path.exists(file_path):
                return True
            
            # 下载文件
            success = self.api_service.download_file(page.url_original, file_path)
            
            if success and os.path.exists(file_path):
                self.logger.debug(f"下载页面成功: {filename}")
                return True
            else:
                self.logger.warning(f"下载页面失败: {filename}")
                return False
                
        except Exception as e:
            self.logger.error(f"下载页面失败: {e}")
            return False
    
    def _process_ugoira(self, artwork: Artwork, save_dir: str) -> None:
        """处理动图(ugoira)"""
        try:
            # 获取动图信息
            ugoira_url = f"https://www.pixiv.net/ajax/illust/{artwork.id}/ugoira_meta"
            ugoira_data = self.api_service.make_request(ugoira_url)
            
            if not ugoira_data or ugoira_data.status_code != 200:
                self.logger.warning(f"无法获取动图 {artwork.id} 的元数据")
                return
            
            meta = ugoira_data.json()['body']
            frames = meta['frames']
            zip_url = meta['originalSrc']
            
            # 下载zip文件
            zip_path = os.path.join(save_dir, 'animation.zip')
            if self.api_service.download_file(zip_url, zip_path):
                # 解压帧文件
                frames_dir = os.path.join(save_dir, 'frames')
                os.makedirs(frames_dir, exist_ok=True)
                
                with zipfile.ZipFile(zip_path, 'r') as zip_file:
                    zip_file.extractall(frames_dir)
                
                # 根据配置处理GIF
                if self.download_config.gif_mode in [GifMode.GIF_ONLY, GifMode.BOTH]:
                    self._create_gif_from_frames(frames_dir, frames, save_dir)
                
                # 根据配置决定是否保留帧文件
                if self.download_config.gif_mode == GifMode.GIF_ONLY:
                    # 删除帧文件目录
                    import shutil
                    shutil.rmtree(frames_dir, ignore_errors=True)
                    os.remove(zip_path)
                
        except Exception as e:
            self.logger.error(f"处理动图失败: {e}")
    
    def _create_gif_from_frames(self, frames_dir: str, frames_info: List[Dict], save_dir: str) -> None:
        """从帧文件创建GIF"""
        if not Image:
            self.logger.warning("PIL未安装，无法创建GIF")
            return
        
        try:
            images = []
            durations = []
            
            for frame in frames_info:
                frame_path = os.path.join(frames_dir, frame['file'])
                if os.path.exists(frame_path):
                    img = Image.open(frame_path)
                    images.append(img)
                    durations.append(frame['delay'])
            
            if images:
                gif_path = os.path.join(save_dir, 'animation.gif')
                images[0].save(
                    gif_path,
                    save_all=True,
                    append_images=images[1:],
                    duration=durations,
                    loop=0
                )
                self.logger.info(f"GIF创建成功: {gif_path}")
                
        except Exception as e:
            self.logger.error(f"创建GIF失败: {e}")
    
    def _create_info_file(self, artwork: Artwork, save_dir: str) -> None:
        """创建作品信息文件"""
        try:
            info_file = os.path.join(save_dir, 'info.txt')
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write(f"标题: {artwork.title}\n")
                f.write(f"作者: {artwork.author_name}\n")
                f.write(f"作品ID: {artwork.id}\n")
                f.write(f"类型: {artwork.type.value}\n")
                f.write(f"页数: {artwork.page_count}\n")
                f.write(f"上传日期: {artwork.upload_date}\n")
                f.write(f"浏览数: {artwork.view_count}\n")
                f.write(f"点赞数: {artwork.like_count}\n")
                f.write(f"收藏数: {artwork.bookmark_count}\n")
                f.write(f"标签: {', '.join(artwork.tags)}\n")
                f.write(f"描述: {artwork.description}\n")
                f.write(f"链接: {artwork.url}\n")
        except Exception as e:
            self.logger.error(f"创建信息文件失败: {e}")
    
    def batch_download(self, artworks: List[Artwork]) -> Dict[str, int]:
        """
        批量下载作品
        
        Args:
            artworks: 作品列表
            
        Returns:
            Dict[str, int]: 下载统计信息
        """
        stats = {
            'total': len(artworks),
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        # 重置停止标志
        self._should_stop = False

        # 快速路径验证
        save_path = self.download_config.save_path
        if not os.path.exists(save_path):
            os.makedirs(save_path, exist_ok=True)

        if self._status_callback:
            self._status_callback(f"开始批量下载 {len(artworks)} 个作品...")

        # 延迟初始化线程池
        self._ensure_executor_initialized()

        if self.executor._shutdown:
            self.logger.error("❌ 线程池已关闭")
            return stats

        # 批量预检查已下载的作品
        if self.download_config.skip_existing:
            # 只在需要时初始化缓存
            self._ensure_cache_initialized()

            self.logger.info("🔍 批量检查已下载作品...")
            start_time = time.time()

            skipped_count = 0
            for artwork in artworks:
                if self._should_stop:
                    break

                # 直接使用缓存检查，避免重复的文件系统操作
                if artwork.id in self._downloaded_cache:
                    artwork.status = ArtworkStatus.SKIPPED
                    artwork.local_path = self._downloaded_cache[artwork.id]
                    skipped_count += 1

            # 过滤掉已下载的作品
            artworks_to_download = [a for a in artworks if a.status != ArtworkStatus.SKIPPED]
            check_time = time.time() - start_time
            self.logger.info(f"📊 预检查完成: {skipped_count} 个已下载, {len(artworks_to_download)} 个待下载 (耗时: {check_time:.2f}秒)")
            stats['skipped'] = skipped_count
        else:
            artworks_to_download = artworks

        if not artworks_to_download:
            self.logger.info("🎉 所有作品都已下载，无需重复下载")
            return stats

        # 使用线程池并发下载
        futures = []
        self.logger.info(f"� 开始提交 {len(artworks_to_download)} 个下载任务...")

        for i, artwork in enumerate(artworks_to_download):
            # 检查是否需要停止
            if self._should_stop:
                self.logger.info("收到停止信号，停止提交新任务")
                break

            if self.download_config.download_limit > 0 and i >= self.download_config.download_limit:
                self.logger.info(f"达到下载限制 {self.download_config.download_limit}，停止提交新任务")
                break

            try:
                future = self.executor.submit(self.download_artwork, artwork)
                futures.append((future, artwork))
            except Exception as e:
                self.logger.error(f"❌ 提交作品 {artwork.id} 失败: {e}")
                stats['failed'] += 1

        self.logger.info(f"🔧 所有任务已提交，共 {len(futures)} 个任务在队列中")
        
        # 收集结果 - 使用更高效的批量处理方式
        completed = 0

        if not futures:
            self.logger.info("📝 没有需要下载的任务")
            return stats

        self.logger.info(f"⏳ 等待 {len(futures)} 个下载任务完成...")

        # 使用as_completed来高效处理完成的任务
        future_to_artwork = {f: a for f, a in futures}

        try:
            # 使用as_completed批量处理，设置合理的超时
            for future in as_completed([f for f, a in futures], timeout=60):
                if self._should_stop:
                    self.logger.info("收到停止信号，停止处理结果")
                    break

                # 获取对应的artwork
                artwork = future_to_artwork[future]

                try:
                    future.result()  # 获取结果，但不需要返回值
                    completed += 1

                    if artwork.status == ArtworkStatus.COMPLETED:
                        stats['success'] += 1
                        self.logger.debug(f"✅ 作品 {artwork.id} 下载成功")
                    elif artwork.status == ArtworkStatus.SKIPPED:
                        stats['skipped'] += 1
                        self.logger.debug(f"⏭️ 作品 {artwork.id} 已跳过")
                    else:
                        stats['failed'] += 1
                        self.logger.debug(f"❌ 作品 {artwork.id} 下载失败")

                    # 更新进度
                    if self._progress_callback:
                        self._progress_callback(completed, len(futures), f"已完成 {completed}/{len(futures)}")

                except Exception as e:
                    stats['failed'] += 1
                    completed += 1
                    self.logger.error(f"下载任务异常: {e}")

        except TimeoutError:
            # 处理超时的任务
            self.logger.warning("部分下载任务超时，正在处理剩余任务...")
            completed = self._wait_for_remaining_futures(futures, stats, completed)
        
        # 等待剩余的未完成任务
        if not self._should_stop and completed < len(futures):
            completed = self._wait_for_remaining_futures(futures, stats, completed)

        # 如果收到停止信号，取消所有剩余任务
        if self._should_stop:
            self.logger.info("收到停止信号，取消剩余任务")
            for future, artwork in futures:
                if not future.done():
                    future.cancel()

        # 更新实际处理的数量
        stats['total'] = len(futures)
        
        status_message = f"批量下载完成: 成功 {stats['success']}, 失败 {stats['failed']}, 跳过 {stats['skipped']}"
        if self._should_stop:
            status_message = f"下载已停止: 成功 {stats['success']}, 失败 {stats['failed']}, 跳过 {stats['skipped']}"
            
        if self._status_callback:
            self._status_callback(status_message)
        
        return stats

    def _wait_for_remaining_futures(self, futures, stats, completed):
        """等待剩余未完成的任务"""
        remaining_futures = [f for f, a in futures if not f.done()]

        if not remaining_futures:
            return completed

        self.logger.info(f"等待剩余 {len(remaining_futures)} 个任务完成...")

        # 等待剩余任务，但不设置过短的超时
        for future, artwork in futures:
            if future.done() or self._should_stop:
                continue

            try:
                # 等待任务完成，设置合理的超时时间
                success = future.result(timeout=30.0)  # 增加到30秒
                completed += 1

                if artwork.status == ArtworkStatus.COMPLETED:
                    stats['success'] += 1
                elif artwork.status == ArtworkStatus.SKIPPED:
                    stats['skipped'] += 1
                else:
                    stats['failed'] += 1

                # 更新进度
                if self._progress_callback:
                    self._progress_callback(completed, len(futures), f"已完成 {completed}/{len(futures)}")

            except TimeoutError:
                # 真正的超时，标记为失败
                stats['failed'] += 1
                completed += 1
                self.logger.warning(f"任务真正超时: {artwork.id}")
            except Exception as e:
                stats['failed'] += 1
                completed += 1
                self.logger.error(f"处理剩余任务异常: {e}")

        return completed
    
    def __del__(self):
        """析构函数，清理线程池"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False) 