@echo off
chcp 65001 >nul
title Pixiv Spider v6.0 启动器

echo.
echo ================================================================
echo 🎨 Pixiv Spider v6.0 - 现代化桌面爬虫工具
echo ================================================================
echo ⚡ 闪电启动 ^| 🎨 现代界面 ^| 🛡️ 稳定可靠
echo.

:: 检查Python是否安装
echo 🐍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请安装Python 3.8+: https://python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: 检查Node.js是否安装
echo 📦 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装或未添加到PATH
    echo 💡 请安装Node.js 16+: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: 运行Python启动器
echo 🚀 启动应用程序...
python start.py

:: 如果启动失败，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 启动失败，请检查以下问题：
    echo    1. Python和Node.js是否正确安装
    echo    2. 网络连接是否正常
    echo    3. 是否有足够的磁盘空间
    echo.
    echo 💡 解决方案：
    echo    - 运行 python cleanup_project.py 清理项目
    echo    - 查看README.md获取详细说明
    echo    - 手动运行: cd electron-gui ^&^& npm run app
    echo.
    pause
    exit /b 1
)

echo.
echo 🎉 启动成功！
echo 💡 可以关闭此窗口，应用程序将继续运行
echo.
timeout /t 3 /nobreak >nul
exit /b 0
