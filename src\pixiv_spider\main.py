"""
Pixiv Spider 主程序入口
"""

import sys
import logging
import argparse
from pathlib import Path
from typing import Optional

from .core import PixivSpider
from .config import ConfigManager
from .models.config import DownloadConfig, SpiderConfig
from .models.exceptions import PixivSpiderError


def setup_logging(level: str = "INFO", log_file: Optional[str] = None) -> None:
    """设置日志"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 基本配置
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=log_format,
        handlers=[]
    )
    
    # 控制台输出
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(log_format))
    logging.getLogger().addHandler(console_handler)
    
    # 文件输出
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Pixiv Spider - 模块化的Pixiv作品爬虫工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s --gui                     # 启动GUI界面
  %(prog)s --mode date --days 7      # 下载过去7天的作品
  %(prog)s --mode search --keyword "初音未来"  # 搜索并下载作品
  %(prog)s --config-dir ./config     # 指定配置目录
        """
    )
    
    # 基本参数
    parser.add_argument(
        "--version", 
        action="version", 
        version="Pixiv Spider 3.0.0"
    )
    
    parser.add_argument(
        "--gui", 
        action="store_true",
        help="启动GUI界面"
    )
    
    parser.add_argument(
        "--config-dir",
        type=str,
        help="配置文件目录"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )
    
    parser.add_argument(
        "--log-file",
        type=str,
        help="日志文件路径"
    )
    
    # 下载参数
    download_group = parser.add_argument_group("下载选项")
    
    download_group.add_argument(
        "--mode",
        choices=["date", "ranking", "search", "user", "bookmark"],
        help="下载模式"
    )
    
    download_group.add_argument(
        "--days",
        type=int,
        help="下载天数（日期模式）"
    )
    
    download_group.add_argument(
        "--keyword",
        type=str,
        help="搜索关键词（搜索模式）"
    )
    
    download_group.add_argument(
        "--user-id",
        type=int,
        help="用户ID（用户模式）"
    )
    
    download_group.add_argument(
        "--save-path",
        type=str,
        help="保存路径"
    )
    
    download_group.add_argument(
        "--start-page",
        type=int,
        help="起始页面"
    )
    
    download_group.add_argument(
        "--end-page",
        type=int,
        help="结束页面"
    )
    
    return parser


def main() -> None:
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger(__name__)
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager(args.config_dir)
        
        # 如果启动GUI
        if args.gui:
            logger.info("启动GUI界面...")
            from .gui import create_gui
            
            app = create_gui(config_manager)
            app.run()
            return
        
        # 创建爬虫实例
        spider = PixivSpider(config_manager)
        
        # 如果提供了命令行参数，更新配置
        if any([args.mode, args.days, args.keyword, args.user_id, args.save_path, 
                args.start_page, args.end_page]):
            
            download_config = spider.download_config
            
            if args.mode:
                from .models.config import DownloadMode
                download_config.download_mode = DownloadMode(args.mode)
            
            if args.days:
                download_config.days = args.days
            
            if args.keyword:
                download_config.search_keyword = args.keyword
            
            if args.user_id:
                download_config.user_id = args.user_id
            
            if args.save_path:
                download_config.save_path = args.save_path
            
            if args.start_page:
                download_config.start_page = args.start_page
            
            if args.end_page:
                download_config.end_page = args.end_page
            
            spider.update_download_config(download_config)
        
        # 进行身份验证
        logger.info("正在进行身份验证...")
        if not spider.authenticate():
            logger.error("身份验证失败，请检查登录状态")
            sys.exit(1)
        
        # 开始下载
        logger.info("开始下载任务...")
        spider.start_download()
        
        logger.info("下载任务完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except PixivSpiderError as e:
        logger.error(f"爬虫错误: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"未知错误: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main() 