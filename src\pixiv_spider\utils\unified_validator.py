"""
统一验证器
解决前后端重复的验证逻辑
"""

from typing import List, Dict, Any, Optional, Tuple
import os
from pathlib import Path
import logging

from ..models.config import DownloadConfig, DownloadMode


class UnifiedValidator:
    """统一验证器，提供一致的验证逻辑"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 错误消息映射
        self.error_messages = {
            'INVALID_PATH': '请选择有效的下载路径',
            'PATH_NOT_WRITABLE': '下载路径无写入权限',
            'INVALID_PAGE_RANGE': '起始页码不能大于结束页码',
            'PAGE_TOO_SMALL': '页码必须大于0',
            'PAGE_TOO_LARGE': '页码不能超过1000',
            'SEARCH_KEYWORD_REQUIRED': '搜索模式下请输入搜索关键词',
            'KEYWORD_TOO_SHORT': '搜索关键词至少需要1个字符',
            'KEYWORD_TOO_LONG': '搜索关键词不能超过100个字符',
            'ARTIST_ID_REQUIRED': '画师模式下请输入画师ID',
            'INVALID_ARTIST_ID': '请输入有效的画师ID（正整数）',
            'INVALID_BOOKMARK_COUNT': '收藏数必须为正整数',
            'BOOKMARK_RANGE_ERROR': '最小收藏数不能大于最大收藏数',
            'INVALID_DAYS': '天数必须大于0'
        }
    
    def validate_download_config(self, config: DownloadConfig) -> List[str]:
        """验证下载配置"""
        errors = []
        
        # 验证基础路径
        errors.extend(self._validate_path(config.save_path))
        
        # 验证页码范围（通用）
        errors.extend(self._validate_page_range(config.start_page, config.end_page))
        
        # 根据下载模式验证
        if config.download_mode == DownloadMode.SEARCH:
            errors.extend(self._validate_search_config(config))
        elif config.download_mode == DownloadMode.USER:
            errors.extend(self._validate_user_config(config))
        elif config.download_mode == DownloadMode.DATE:
            errors.extend(self._validate_date_config(config))
        elif config.download_mode == DownloadMode.RANKING:
            errors.extend(self._validate_ranking_config(config))
        
        # 验证收藏过滤
        errors.extend(self._validate_bookmark_filter(config))
        
        return errors
    
    def _validate_path(self, path: str) -> List[str]:
        """验证路径"""
        errors = []
        
        if not path or not path.strip():
            errors.append(self.error_messages['INVALID_PATH'])
            return errors
        
        try:
            path_obj = Path(path)
            
            # 检查父目录是否存在且可写
            if path_obj.exists():
                if not os.access(path, os.W_OK):
                    errors.append(self.error_messages['PATH_NOT_WRITABLE'])
            else:
                # 检查父目录
                parent = path_obj.parent
                if not parent.exists():
                    try:
                        parent.mkdir(parents=True, exist_ok=True)
                    except Exception:
                        errors.append(f'无法创建目录: {parent}')
                elif not os.access(parent, os.W_OK):
                    errors.append(self.error_messages['PATH_NOT_WRITABLE'])
                    
        except Exception as e:
            errors.append(f'路径验证失败: {str(e)}')
        
        return errors
    
    def _validate_page_range(self, start_page: int, end_page: int) -> List[str]:
        """验证页码范围"""
        errors = []
        
        if start_page < 1:
            errors.append(self.error_messages['PAGE_TOO_SMALL'])
        
        if end_page < 1:
            errors.append(self.error_messages['PAGE_TOO_SMALL'])
        
        if start_page > end_page:
            errors.append(self.error_messages['INVALID_PAGE_RANGE'])
        
        if start_page > 1000 or end_page > 1000:
            errors.append(self.error_messages['PAGE_TOO_LARGE'])
        
        return errors
    
    def _validate_search_config(self, config: DownloadConfig) -> List[str]:
        """验证搜索配置"""
        errors = []
        
        if not config.search_keyword:
            errors.append(self.error_messages['SEARCH_KEYWORD_REQUIRED'])
        else:
            if len(config.search_keyword.strip()) < 1:
                errors.append(self.error_messages['KEYWORD_TOO_SHORT'])
            if len(config.search_keyword) > 100:
                errors.append(self.error_messages['KEYWORD_TOO_LONG'])
        
        return errors
    
    def _validate_user_config(self, config: DownloadConfig) -> List[str]:
        """验证画师配置"""
        errors = []
        
        if not config.user_id or config.user_id <= 0:
            errors.append(self.error_messages['ARTIST_ID_REQUIRED'])
        elif not isinstance(config.user_id, int):
            errors.append(self.error_messages['INVALID_ARTIST_ID'])
        
        return errors
    
    def _validate_date_config(self, config: DownloadConfig) -> List[str]:
        """验证关注配置"""
        errors = []
        
        if hasattr(config, 'days') and config.days <= 0:
            errors.append(self.error_messages['INVALID_DAYS'])
        
        return errors
    
    def _validate_ranking_config(self, config: DownloadConfig) -> List[str]:
        """验证排行榜配置"""
        # 排行榜模式通常使用固定配置，验证较少
        return []
    
    def _validate_bookmark_filter(self, config: DownloadConfig) -> List[str]:
        """验证收藏过滤配置"""
        errors = []
        
        if hasattr(config, 'min_bookmarks') and config.min_bookmarks < 0:
            errors.append(self.error_messages['INVALID_BOOKMARK_COUNT'])
        
        if hasattr(config, 'max_bookmarks') and config.max_bookmarks < 0:
            errors.append(self.error_messages['INVALID_BOOKMARK_COUNT'])
        
        if (hasattr(config, 'min_bookmarks') and hasattr(config, 'max_bookmarks') and
            config.max_bookmarks > 0 and config.min_bookmarks > config.max_bookmarks):
            errors.append(self.error_messages['BOOKMARK_RANGE_ERROR'])
        
        return errors
    
    def validate_dict_config(self, config_dict: Dict[str, Any]) -> List[str]:
        """验证字典格式的配置"""
        errors = []
        
        # 验证基础字段
        if not config_dict.get('save_path'):
            errors.append(self.error_messages['INVALID_PATH'])
        
        # 验证页码
        start_page = config_dict.get('start_page', 1)
        end_page = config_dict.get('end_page', 1)
        errors.extend(self._validate_page_range(start_page, end_page))
        
        # 根据模式验证
        download_mode = config_dict.get('download_mode', 'date')
        
        if download_mode == 'search':
            if not config_dict.get('search_keyword'):
                errors.append(self.error_messages['SEARCH_KEYWORD_REQUIRED'])
        
        elif download_mode == 'user':
            user_id = config_dict.get('user_id', 0)
            if not user_id or user_id <= 0:
                errors.append(self.error_messages['ARTIST_ID_REQUIRED'])
        
        elif download_mode == 'date':
            days = config_dict.get('days', 7)
            if days <= 0:
                errors.append(self.error_messages['INVALID_DAYS'])
        
        return errors
    
    def get_error_message(self, error_code: str) -> str:
        """获取错误消息"""
        return self.error_messages.get(error_code, '未知错误')
    
    def format_errors(self, errors: List[str]) -> str:
        """格式化错误列表"""
        if not errors:
            return ''

        if len(errors) == 1:
            return errors[0]

        return '; '.join(errors)

    def has_errors(self, errors: List[str]) -> bool:
        """检查是否有错误"""
        return bool(errors)

    # 从validation_utils.py合并的静态验证方法
    @staticmethod
    def is_valid_user_id(user_id: str) -> bool:
        """验证用户ID是否有效"""
        try:
            uid = int(user_id)
            return uid > 0
        except (ValueError, TypeError):
            return False

    @staticmethod
    def is_valid_page_range_simple(start_page: str, end_page: str) -> Tuple[bool, str]:
        """简单的页码范围验证（兼容字符串输入）"""
        try:
            start = int(start_page)
            end = int(end_page)

            if start < 1:
                return False, "起始页码必须大于0"

            if end < start:
                return False, "结束页码必须大于等于起始页码"

            if end - start > 100:
                return False, "页码范围不能超过100页"

            return True, ""

        except (ValueError, TypeError):
            return False, "页码必须是有效的数字"

    @staticmethod
    def is_valid_days(days: str) -> Tuple[bool, str]:
        """验证天数是否有效"""
        try:
            day_count = int(days)

            if day_count < 1:
                return False, "天数必须大于0"

            if day_count > 365:
                return False, "天数不能超过365天"

            return True, ""

        except (ValueError, TypeError):
            return False, "天数必须是有效的数字"

    @staticmethod
    def is_valid_bookmark_count(count: str) -> Tuple[bool, str]:
        """验证收藏数是否有效"""
        try:
            bookmark_count = int(count)

            if bookmark_count < 0:
                return False, "收藏数不能为负数"

            if bookmark_count > 1000000:
                return False, "收藏数过大"

            return True, ""

        except (ValueError, TypeError):
            return False, "收藏数必须是有效的数字"


# 创建全局实例
unified_validator = UnifiedValidator()
